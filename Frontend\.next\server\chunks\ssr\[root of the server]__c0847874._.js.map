{"version": 3, "sources": [], "sections": [{"offset": {"line": 6, "column": 0}, "map": {"version": 3, "sources": [], "names": [], "mappings": "", "debugId": null}}, {"offset": {"line": 6, "column": 0}, "map": {"version": 3, "sources": [], "names": [], "mappings": "A"}}, {"offset": {"line": 24, "column": 0}, "map": {"version": 3, "sources": ["file://C%3A/Users/<USER>/Desktop/Jerrax/Frontend/src/app/%28Home%29/education/EducationClient.js/proxy.mjs"], "sourcesContent": ["import { registerClientReference } from \"react-server-dom-turbopack/server.edge\";\nexport default registerClientReference(\n    function() { throw new Error(\"Attempted to call the default export of [project]/src/app/(Home)/education/EducationClient.js <module evaluation> from the server, but it's on the client. It's not possible to invoke a client function from the server, it can only be rendered as a Component or passed to props of a Client Component.\"); },\n    \"[project]/src/app/(Home)/education/EducationClient.js <module evaluation>\",\n    \"default\",\n);\n"], "names": [], "mappings": ";;;AAAA;;uCACe,CAAA,GAAA,qPAAA,CAAA,0BAAuB,AAAD,EACjC;IAAa,MAAM,IAAI,MAAM;AAA+S,GAC5U,6EACA", "debugId": null}}, {"offset": {"line": 32, "column": 0}, "map": {"version": 3, "sources": [], "names": [], "mappings": "A"}}, {"offset": {"line": 38, "column": 0}, "map": {"version": 3, "sources": ["file://C%3A/Users/<USER>/Desktop/Jerrax/Frontend/src/app/%28Home%29/education/EducationClient.js/proxy.mjs"], "sourcesContent": ["import { registerClientReference } from \"react-server-dom-turbopack/server.edge\";\nexport default registerClientReference(\n    function() { throw new Error(\"Attempted to call the default export of [project]/src/app/(Home)/education/EducationClient.js from the server, but it's on the client. It's not possible to invoke a client function from the server, it can only be rendered as a Component or passed to props of a Client Component.\"); },\n    \"[project]/src/app/(Home)/education/EducationClient.js\",\n    \"default\",\n);\n"], "names": [], "mappings": ";;;AAAA;;uCACe,CAAA,GAAA,qPAAA,CAAA,0BAAuB,AAAD,EACjC;IAAa,MAAM,IAAI,MAAM;AAA2R,GACxT,yDACA", "debugId": null}}, {"offset": {"line": 46, "column": 0}, "map": {"version": 3, "sources": [], "names": [], "mappings": "A"}}, {"offset": {"line": 52, "column": 0}, "map": {"version": 3, "sources": [], "names": [], "mappings": "", "debugId": null}}, {"offset": {"line": 56, "column": 0}, "map": {"version": 3, "sources": [], "names": [], "mappings": "A"}}, {"offset": {"line": 62, "column": 0}, "map": {"version": 3, "sources": ["file://C%3A/Users/<USER>/Desktop/Jerrax/Frontend/src/Seo/Meta/MetadataUtils.js"], "sourcesContent": ["/**\n * Next.js 15 App Router Metadata Utilities\n * \n * This file provides utilities for generating metadata objects that work with\n * Next.js 15's metadata API to properly place meta tags and JSON-LD schemas\n * in the <head> section via Server-Side Rendering (SSR).\n * \n * Usage:\n * export async function generateMetadata() {\n *   const schemas = [generateOrganizationSchema(), generateWebsiteSchema()];\n *   return generatePageMetadata(metaProps, schemas);\n * }\n */\n\n/**\n * Generate metadata object for Next.js 15 App Router\n * This handles meta tags, Open Graph, Twitter cards, etc.\n * Note: JSON-LD schemas are handled separately via JsonLdSSR component\n *\n * @param {Object} props - Meta properties\n * @param {Array} schemas - JSON-LD schemas array (for compatibility, not used)\n * @returns {Object} - Next.js metadata object\n */\nexport function generatePageMetadata(props = {}, schemas = []) {\n  const environment = process.env.NEXT_PUBLIC_ENVIRONMENT;\n\n  const defaultMeta = {\n    title: \"TradeReply: Optimize Your Trading Strategies & Analytics\",\n    description: \"Optimize your trades with TradeReply.com. Access powerful trading strategies, real-time analytics, and tools for crypto and stock market success.\",\n    canonical: \"https://www.tradereply.com/\",\n    ogTitle: \"TradeReply: Optimize Your Trading Strategies & Analytics\",\n    ogDescription: \"Access powerful trading strategies, real-time analytics, and tools for crypto and stock market success with TradeReply.com.\",\n    ogSiteName: \"TradeReply\",\n    ogImage: \"https://cdn.tradereply.com/dev/misc/tradereply-trading-analytics-og.png\",\n    twitterTitle: \"TradeReply: Optimize Your Trading Strategies & Analytics\",\n    twitterDescription: \"Access powerful trading strategies, real-time analytics, and tools for crypto and stock market success with TradeReply.com.\",\n    twitterImage: 'https://cdn.tradereply.com/dev/misc/tradereply-trading-analytics-og.png'\n  };\n\n  const isNoIndex = props?.noindex === true;\n  const robotsContent = props?.robots || (isNoIndex ? \"noindex, follow\" : \"index, follow\");\n\n  // Build the metadata object\n  const metadata = {\n    title: props?.title || defaultMeta.title,\n    description: props?.description || defaultMeta.description,\n    robots: robotsContent,\n    \n    // Open Graph\n    openGraph: {\n      title: props?.og_title || defaultMeta.ogTitle,\n      description: props?.og_description || defaultMeta.ogDescription,\n      siteName: props?.og_site_name || defaultMeta.ogSiteName,\n      images: [\n        {\n          url: defaultMeta.ogImage,\n          width: 1200,\n          height: 630,\n          alt: props?.og_title || defaultMeta.ogTitle,\n        },\n      ],\n      locale: 'en_US',\n      type: 'website',\n    },\n\n    // Twitter\n    twitter: {\n      card: 'summary_large_image',\n      title: props?.twitter_title || defaultMeta.twitterTitle,\n      description: props?.twitter_description || defaultMeta.twitterDescription,\n      images: [defaultMeta.ogImage],\n      site: '@JoinTradeReply',\n    },\n\n    // Icons\n    icons: {\n      icon: [\n        {\n          url: `https://cdn.tradereply.com/${environment}/site-assets/tradereply-favicon.ico`,\n          type: 'image/x-icon',\n        },\n        {\n          url: `https://cdn.tradereply.com/${environment}/site-assets/tradereply-favicon.svg`,\n          type: 'image/svg+xml',\n        },\n      ],\n    },\n\n    // Other metadata\n    other: {},\n  };\n\n  // Add canonical URL if provided and not noindex\n  if (props?.canonical_link?.trim() && !isNoIndex) {\n    metadata.alternates = {\n      canonical: props.canonical_link,\n    };\n  }\n\n  // Add rel=\"next\" if provided\n  if (props?.rel_next) {\n    if (!metadata.alternates) metadata.alternates = {};\n    metadata.alternates.next = props.rel_next;\n  }\n\n  return metadata;\n}\n\n/**\n * Generate JSON-LD script tags for the head section\n * Each schema is placed in a separate script tag as per SEO best practices\n * \n * @param {Array} schemas - Array of JSON-LD schema objects\n * @returns {Array} - Array of script tag objects for Next.js\n */\nexport function generateJsonLdScripts(schemas = []) {\n  if (!schemas || schemas.length === 0) {\n    return [];\n  }\n\n  return schemas\n    .filter(schema => schema !== null && schema !== undefined)\n    .map((schema, index) => ({\n      id: `jsonld-${index}`,\n      type: 'application/ld+json',\n      children: JSON.stringify(schema, null, 0),\n    }));\n}\n\n/**\n * Component for rendering JSON-LD schemas in the head section\n * This should be used in the root layout or page components\n * \n * @param {Array} schemas - Array of JSON-LD schema objects\n * @returns {JSX.Element} - Script tags for JSON-LD schemas\n */\nexport function JsonLdScripts({ schemas = [] }) {\n  if (!schemas || schemas.length === 0) {\n    return null;\n  }\n\n  return (\n    <>\n      {schemas\n        .filter(schema => schema !== null && schema !== undefined)\n        .map((schema, index) => (\n          <script\n            key={`jsonld-${index}`}\n            type=\"application/ld+json\"\n            dangerouslySetInnerHTML={{\n              __html: JSON.stringify(schema, null, 0)\n            }}\n          />\n        ))}\n    </>\n  );\n}\n\n/**\n * Generate complete metadata with JSON-LD schemas for Next.js 15\n * This is the main function to use in page components\n * \n * @param {Object} props - Meta properties\n * @param {Array} schemas - JSON-LD schemas array\n * @returns {Object} - Complete Next.js metadata object with JSON-LD\n */\nexport function generateMetadataWithSchemas(props = {}, schemas = []) {\n  const metadata = generatePageMetadata(props, schemas);\n  \n  // Add JSON-LD schemas to other metadata\n  if (schemas && schemas.length > 0) {\n    schemas\n      .filter(schema => schema !== null && schema !== undefined)\n      .forEach((schema, index) => {\n        metadata.other[`jsonld-script-${index}`] = {\n          type: 'application/ld+json',\n          content: JSON.stringify(schema, null, 0),\n        };\n      });\n  }\n\n  return metadata;\n}\n\n/**\n * Utility function to merge default metadata with page-specific metadata\n * \n * @param {Object} defaultMeta - Default metadata object\n * @param {Object} pageMeta - Page-specific metadata object\n * @returns {Object} - Merged metadata object\n */\nexport function mergeMetadata(defaultMeta = {}, pageMeta = {}) {\n  return {\n    ...defaultMeta,\n    ...pageMeta,\n    openGraph: {\n      ...defaultMeta.openGraph,\n      ...pageMeta.openGraph,\n    },\n    twitter: {\n      ...defaultMeta.twitter,\n      ...pageMeta.twitter,\n    },\n    other: {\n      ...defaultMeta.other,\n      ...pageMeta.other,\n    },\n  };\n}\n\n/**\n * Utility function to validate JSON-LD schema objects for SSR\n *\n * @param {Object} schema - JSON-LD schema object to validate\n * @returns {boolean} - True if schema is valid\n */\nexport function validateJsonLdSchema(schema) {\n  if (!schema || typeof schema !== 'object') {\n    return false;\n  }\n\n  // Check for required @context and @type properties\n  if (!schema['@context'] || !schema['@type']) {\n    return false;\n  }\n\n  // Validate @context\n  if (typeof schema['@context'] !== 'string' || !schema['@context'].includes('schema.org')) {\n    return false;\n  }\n\n  // Validate @type\n  if (typeof schema['@type'] !== 'string' || schema['@type'].trim().length === 0) {\n    return false;\n  }\n\n  return true;\n}\n\n/**\n * Utility function to clean and prepare schemas for SSR\n * Ensures ISO 8601 date format and removes invalid data\n *\n * @param {Object} schema - Schema object to clean\n * @returns {Object} - Cleaned schema object\n */\nexport function cleanSchemaForSSR(schema) {\n  if (!schema) return null;\n\n  const cleanedSchema = { ...schema };\n\n  // Common date fields to check and format to ISO 8601\n  const dateFields = ['datePublished', 'dateModified', 'dateCreated', 'uploadDate'];\n\n  dateFields.forEach(field => {\n    if (cleanedSchema[field] && typeof cleanedSchema[field] === 'string') {\n      try {\n        const date = new Date(cleanedSchema[field]);\n        if (!isNaN(date.getTime())) {\n          cleanedSchema[field] = date.toISOString();\n        }\n      } catch (error) {\n        console.warn(`Invalid date format for ${field}:`, cleanedSchema[field]);\n        // Remove invalid date rather than keeping it\n        delete cleanedSchema[field];\n      }\n    }\n  });\n\n  return cleanedSchema;\n}\n\n/**\n * Default metadata configuration for TradeReply\n */\nexport const DEFAULT_METADATA = {\n  title: \"TradeReply: Optimize Your Trading Strategies & Analytics\",\n  description: \"Optimize your trades with TradeReply.com. Access powerful trading strategies, real-time analytics, and tools for crypto and stock market success.\",\n  robots: \"index, follow\",\n  openGraph: {\n    title: \"TradeReply: Optimize Your Trading Strategies & Analytics\",\n    description: \"Access powerful trading strategies, real-time analytics, and tools for crypto and stock market success with TradeReply.com.\",\n    siteName: \"TradeReply\",\n    images: [\n      {\n        url: \"https://cdn.tradereply.com/dev/misc/tradereply-trading-analytics-og.png\",\n        width: 1200,\n        height: 630,\n        alt: \"TradeReply Trading Analytics\",\n      },\n    ],\n    locale: 'en_US',\n    type: 'website',\n  },\n  twitter: {\n    card: 'summary_large_image',\n    title: \"TradeReply: Optimize Your Trading Strategies & Analytics\",\n    description: \"Access powerful trading strategies, real-time analytics, and tools for crypto and stock market success with TradeReply.com.\",\n    images: [\"https://cdn.tradereply.com/dev/misc/tradereply-trading-analytics-og.png\"],\n    site: '@JoinTradeReply',\n  },\n};\n"], "names": [], "mappings": "AAAA;;;;;;;;;;;;CAYC,GAED;;;;;;;;CAQC;;;;;;;;;;;;AACM,SAAS,qBAAqB,QAAQ,CAAC,CAAC,EAAE,UAAU,EAAE;IAC3D,MAAM;IAEN,MAAM,cAAc;QAClB,OAAO;QACP,aAAa;QACb,WAAW;QACX,SAAS;QACT,eAAe;QACf,YAAY;QACZ,SAAS;QACT,cAAc;QACd,oBAAoB;QACpB,cAAc;IAChB;IAEA,MAAM,YAAY,OAAO,YAAY;IACrC,MAAM,gBAAgB,OAAO,UAAU,CAAC,YAAY,oBAAoB,eAAe;IAEvF,4BAA4B;IAC5B,MAAM,WAAW;QACf,OAAO,OAAO,SAAS,YAAY,KAAK;QACxC,aAAa,OAAO,eAAe,YAAY,WAAW;QAC1D,QAAQ;QAER,aAAa;QACb,WAAW;YACT,OAAO,OAAO,YAAY,YAAY,OAAO;YAC7C,aAAa,OAAO,kBAAkB,YAAY,aAAa;YAC/D,UAAU,OAAO,gBAAgB,YAAY,UAAU;YACvD,QAAQ;gBACN;oBACE,KAAK,YAAY,OAAO;oBACxB,OAAO;oBACP,QAAQ;oBACR,KAAK,OAAO,YAAY,YAAY,OAAO;gBAC7C;aACD;YACD,QAAQ;YACR,MAAM;QACR;QAEA,UAAU;QACV,SAAS;YACP,MAAM;YACN,OAAO,OAAO,iBAAiB,YAAY,YAAY;YACvD,aAAa,OAAO,uBAAuB,YAAY,kBAAkB;YACzE,QAAQ;gBAAC,YAAY,OAAO;aAAC;YAC7B,MAAM;QACR;QAEA,QAAQ;QACR,OAAO;YACL,MAAM;gBACJ;oBACE,KAAK,CAAC,2BAA2B,EAAE,YAAY,mCAAmC,CAAC;oBACnF,MAAM;gBACR;gBACA;oBACE,KAAK,CAAC,2BAA2B,EAAE,YAAY,mCAAmC,CAAC;oBACnF,MAAM;gBACR;aACD;QACH;QAEA,iBAAiB;QACjB,OAAO,CAAC;IACV;IAEA,gDAAgD;IAChD,IAAI,OAAO,gBAAgB,UAAU,CAAC,WAAW;QAC/C,SAAS,UAAU,GAAG;YACpB,WAAW,MAAM,cAAc;QACjC;IACF;IAEA,6BAA6B;IAC7B,IAAI,OAAO,UAAU;QACnB,IAAI,CAAC,SAAS,UAAU,EAAE,SAAS,UAAU,GAAG,CAAC;QACjD,SAAS,UAAU,CAAC,IAAI,GAAG,MAAM,QAAQ;IAC3C;IAEA,OAAO;AACT;AASO,SAAS,sBAAsB,UAAU,EAAE;IAChD,IAAI,CAAC,WAAW,QAAQ,MAAM,KAAK,GAAG;QACpC,OAAO,EAAE;IACX;IAEA,OAAO,QACJ,MAAM,CAAC,CAAA,SAAU,WAAW,QAAQ,WAAW,WAC/C,GAAG,CAAC,CAAC,QAAQ,QAAU,CAAC;YACvB,IAAI,CAAC,OAAO,EAAE,OAAO;YACrB,MAAM;YACN,UAAU,KAAK,SAAS,CAAC,QAAQ,MAAM;QACzC,CAAC;AACL;AASO,SAAS,cAAc,EAAE,UAAU,EAAE,EAAE;IAC5C,IAAI,CAAC,WAAW,QAAQ,MAAM,KAAK,GAAG;QACpC,OAAO;IACT;IAEA,qBACE;kBACG,QACE,MAAM,CAAC,CAAA,SAAU,WAAW,QAAQ,WAAW,WAC/C,GAAG,CAAC,CAAC,QAAQ,sBACZ,8OAAC;gBAEC,MAAK;gBACL,yBAAyB;oBACvB,QAAQ,KAAK,SAAS,CAAC,QAAQ,MAAM;gBACvC;eAJK,CAAC,OAAO,EAAE,OAAO;;;;;;AASlC;AAUO,SAAS,4BAA4B,QAAQ,CAAC,CAAC,EAAE,UAAU,EAAE;IAClE,MAAM,WAAW,qBAAqB,OAAO;IAE7C,wCAAwC;IACxC,IAAI,WAAW,QAAQ,MAAM,GAAG,GAAG;QACjC,QACG,MAAM,CAAC,CAAA,SAAU,WAAW,QAAQ,WAAW,WAC/C,OAAO,CAAC,CAAC,QAAQ;YAChB,SAAS,KAAK,CAAC,CAAC,cAAc,EAAE,OAAO,CAAC,GAAG;gBACzC,MAAM;gBACN,SAAS,KAAK,SAAS,CAAC,QAAQ,MAAM;YACxC;QACF;IACJ;IAEA,OAAO;AACT;AASO,SAAS,cAAc,cAAc,CAAC,CAAC,EAAE,WAAW,CAAC,CAAC;IAC3D,OAAO;QACL,GAAG,WAAW;QACd,GAAG,QAAQ;QACX,WAAW;YACT,GAAG,YAAY,SAAS;YACxB,GAAG,SAAS,SAAS;QACvB;QACA,SAAS;YACP,GAAG,YAAY,OAAO;YACtB,GAAG,SAAS,OAAO;QACrB;QACA,OAAO;YACL,GAAG,YAAY,KAAK;YACpB,GAAG,SAAS,KAAK;QACnB;IACF;AACF;AAQO,SAAS,qBAAqB,MAAM;IACzC,IAAI,CAAC,UAAU,OAAO,WAAW,UAAU;QACzC,OAAO;IACT;IAEA,mDAAmD;IACnD,IAAI,CAAC,MAAM,CAAC,WAAW,IAAI,CAAC,MAAM,CAAC,QAAQ,EAAE;QAC3C,OAAO;IACT;IAEA,oBAAoB;IACpB,IAAI,OAAO,MAAM,CAAC,WAAW,KAAK,YAAY,CAAC,MAAM,CAAC,WAAW,CAAC,QAAQ,CAAC,eAAe;QACxF,OAAO;IACT;IAEA,iBAAiB;IACjB,IAAI,OAAO,MAAM,CAAC,QAAQ,KAAK,YAAY,MAAM,CAAC,QAAQ,CAAC,IAAI,GAAG,MAAM,KAAK,GAAG;QAC9E,OAAO;IACT;IAEA,OAAO;AACT;AASO,SAAS,kBAAkB,MAAM;IACtC,IAAI,CAAC,QAAQ,OAAO;IAEpB,MAAM,gBAAgB;QAAE,GAAG,MAAM;IAAC;IAElC,qDAAqD;IACrD,MAAM,aAAa;QAAC;QAAiB;QAAgB;QAAe;KAAa;IAEjF,WAAW,OAAO,CAAC,CAAA;QACjB,IAAI,aAAa,CAAC,MAAM,IAAI,OAAO,aAAa,CAAC,MAAM,KAAK,UAAU;YACpE,IAAI;gBACF,MAAM,OAAO,IAAI,KAAK,aAAa,CAAC,MAAM;gBAC1C,IAAI,CAAC,MAAM,KAAK,OAAO,KAAK;oBAC1B,aAAa,CAAC,MAAM,GAAG,KAAK,WAAW;gBACzC;YACF,EAAE,OAAO,OAAO;gBACd,QAAQ,IAAI,CAAC,CAAC,wBAAwB,EAAE,MAAM,CAAC,CAAC,EAAE,aAAa,CAAC,MAAM;gBACtE,6CAA6C;gBAC7C,OAAO,aAAa,CAAC,MAAM;YAC7B;QACF;IACF;IAEA,OAAO;AACT;AAKO,MAAM,mBAAmB;IAC9B,OAAO;IACP,aAAa;IACb,QAAQ;IACR,WAAW;QACT,OAAO;QACP,aAAa;QACb,UAAU;QACV,QAAQ;YACN;gBACE,KAAK;gBACL,OAAO;gBACP,QAAQ;gBACR,KAAK;YACP;SACD;QACD,QAAQ;QACR,MAAM;IACR;IACA,SAAS;QACP,MAAM;QACN,OAAO;QACP,aAAa;QACb,QAAQ;YAAC;SAA0E;QACnF,MAAM;IACR;AACF", "debugId": null}}, {"offset": {"line": 303, "column": 0}, "map": {"version": 3, "sources": [], "names": [], "mappings": "A"}}, {"offset": {"line": 397, "column": 0}, "map": {"version": 3, "sources": ["file://C%3A/Users/<USER>/Desktop/Jerrax/Frontend/src/utils/axiosInstance.js"], "sourcesContent": ["import axios from 'axios';\r\nimport Cookies from 'js-cookie';\r\nimport toast from 'react-hot-toast';\r\n\r\nconst axiosInstance = axios.create({\r\n  baseURL: `${process.env.NEXT_PUBLIC_API_BASE_URL}/api/v1/`,\r\n  withCredentials: true,\r\n  headers: {\r\n    \"Accept\": \"application/json\"\r\n  },\r\n});\r\n\r\n// Flag to prevent multiple simultaneous logout redirects\r\nlet isRedirecting = false;\r\n\r\naxiosInstance.interceptors.request.use(\r\n  (config) => {\r\n    const token = Cookies.get(\"authToken\"); // Fetch latest token\r\n    if (token) {\r\n      config.headers.Authorization = `Bearer ${token}`;\r\n    }\r\n    return config;\r\n  },\r\n  (error) => Promise.reject(error)\r\n);\r\n\r\n// Response Interceptor: Handle Unauthorized Errors\r\naxiosInstance.interceptors.response.use(\r\n  (response) => response,\r\n  (error) => {\r\n    if (error.response) {\r\n      const { status } = error.response;\r\n\r\n      if (status === 401) {\r\n        handleUnauthorizedAccess();\r\n      } else if (status === 404) {\r\n        console.error('Resource not found!');\r\n      } else if (status >= 500) {\r\n        console.error('Server error! Please try again later.');\r\n      }\r\n    } else {\r\n      console.error('Network error or request timeout.');\r\n    }\r\n\r\n    return Promise.reject(error);\r\n  }\r\n);\r\n\r\n/**\r\n * Handle unauthorized access with proper cleanup and redirect\r\n */\r\nfunction handleUnauthorizedAccess() {\r\n  // Prevent multiple simultaneous redirects\r\n  if (isRedirecting) {\r\n    return;\r\n  }\r\n\r\n  isRedirecting = true;\r\n\r\n  console.log('Unauthorized access detected. Logging out...');\r\n\r\n  // Clear all authentication data\r\n  Cookies.remove(\"authToken\");\r\n  localStorage.removeItem(\"user\");\r\n  localStorage.removeItem(\"lastActivity\");\r\n  localStorage.setItem(\"loggedOut\", Date.now());\r\n  sessionStorage.clear();\r\n\r\n  // Show user notification\r\n  toast.error('Your session has expired. Please log in again.', {\r\n    duration: 4000,\r\n    position: 'top-center',\r\n  });\r\n\r\n  // Use window.location for redirect (most reliable method)\r\n  if (typeof window !== 'undefined') {\r\n    // Check if we're already on login page to prevent redirect loop\r\n    if (window.location.pathname !== '/login') {\r\n      window.location.href = '/login';\r\n    }\r\n  }\r\n\r\n  // Reset redirect flag after a delay\r\n  setTimeout(() => {\r\n    isRedirecting = false;\r\n  }, 1000);\r\n}\r\n\r\nexport default axiosInstance;\r\n"], "names": [], "mappings": ";;;AAAA;AACA;AACA;;;;AAEA,MAAM,gBAAgB,qIAAA,CAAA,UAAK,CAAC,MAAM,CAAC;IACjC,SAAS,6DAAwC,QAAQ,CAAC;IAC1D,iBAAiB;IACjB,SAAS;QACP,UAAU;IACZ;AACF;AAEA,yDAAyD;AACzD,IAAI,gBAAgB;AAEpB,cAAc,YAAY,CAAC,OAAO,CAAC,GAAG,CACpC,CAAC;IACC,MAAM,QAAQ,qJAAA,CAAA,UAAO,CAAC,GAAG,CAAC,cAAc,qBAAqB;IAC7D,IAAI,OAAO;QACT,OAAO,OAAO,CAAC,aAAa,GAAG,CAAC,OAAO,EAAE,OAAO;IAClD;IACA,OAAO;AACT,GACA,CAAC,QAAU,QAAQ,MAAM,CAAC;AAG5B,mDAAmD;AACnD,cAAc,YAAY,CAAC,QAAQ,CAAC,GAAG,CACrC,CAAC,WAAa,UACd,CAAC;IACC,IAAI,MAAM,QAAQ,EAAE;QAClB,MAAM,EAAE,MAAM,EAAE,GAAG,MAAM,QAAQ;QAEjC,IAAI,WAAW,KAAK;YAClB;QACF,OAAO,IAAI,WAAW,KAAK;YACzB,QAAQ,KAAK,CAAC;QAChB,OAAO,IAAI,UAAU,KAAK;YACxB,QAAQ,KAAK,CAAC;QAChB;IACF,OAAO;QACL,QAAQ,KAAK,CAAC;IAChB;IAEA,OAAO,QAAQ,MAAM,CAAC;AACxB;AAGF;;CAEC,GACD,SAAS;IACP,0CAA0C;IAC1C,IAAI,eAAe;QACjB;IACF;IAEA,gBAAgB;IAEhB,QAAQ,GAAG,CAAC;IAEZ,gCAAgC;IAChC,qJAAA,CAAA,UAAO,CAAC,MAAM,CAAC;IACf,aAAa,UAAU,CAAC;IACxB,aAAa,UAAU,CAAC;IACxB,aAAa,OAAO,CAAC,aAAa,KAAK,GAAG;IAC1C,eAAe,KAAK;IAEpB,yBAAyB;IACzB,uJAAA,CAAA,UAAK,CAAC,KAAK,CAAC,kDAAkD;QAC5D,UAAU;QACV,UAAU;IACZ;IAEA,0DAA0D;IAC1D,uCAAmC;;IAKnC;IAEA,oCAAoC;IACpC,WAAW;QACT,gBAAgB;IAClB,GAAG;AACL;uCAEe", "debugId": null}}, {"offset": {"line": 468, "column": 0}, "map": {"version": 3, "sources": [], "names": [], "mappings": "A"}}, {"offset": {"line": 474, "column": 0}, "map": {"version": 3, "sources": ["file://C%3A/Users/<USER>/Desktop/Jerrax/Frontend/src/utils/apiUtils.js"], "sourcesContent": ["import axiosInstance from './axiosInstance';\r\nimport axios from 'axios';\r\n\r\n// GET Request Utility\r\nexport const get = async (url, params = {}) => {\r\n  try {\r\n    const response = await axiosInstance.get(url, { params });\r\n    return response.data;\r\n  } catch (error) {\r\n    if (error.name === 'AbortError' || axios.isCancel(error)) { // Handle both AbortError and Axios cancel\r\n      console.log('Request aborted:', url); // Optional quiet log\r\n      return null; // Or throw if needed\r\n    }\r\n    console.error('Error with GET request:', error);\r\n    throw error;\r\n  }\r\n};\r\n\r\nexport const getBrokers = async (url, params = {}) => {\r\n  try {\r\n    const response = await axiosInstance.get(url, { params });\r\n    return response.data;\r\n  } catch (error) {\r\n    console.error('Error with GET request:', error);\r\n    throw error; // Re-throw for further handling\r\n  }\r\n};\r\n\r\n// POST Request Utility\r\nexport const post = async (url, data) => {\r\n  try {\r\n    const response = await axiosInstance.post(url, data);\r\n    return response.data;\r\n  } catch (error) {\r\n    console.error('Error with POST request:', error);\r\n    throw error;\r\n  }\r\n};\r\n\r\n// PUT Request Utility\r\nexport const put = async (url, data) => {\r\n  try {\r\n    const response = await axiosInstance.put(url, data);\r\n    return response.data;\r\n  } catch (error) {\r\n    console.error('Error with PUT request:', error);\r\n    throw error;\r\n  }\r\n};\r\n\r\n// DELETE Request Utility\r\nexport const deleteRequest = async (url) => {\r\n  try {\r\n    const response = await axiosInstance.delete(url);\r\n    return response.data;\r\n  } catch (error) {\r\n    console.error('Error with DELETE request:', error);\r\n    throw error;\r\n  }\r\n};\r\n\r\n// Username management utilities\r\nexport const updateUsername = async (username) => {\r\n  try {\r\n    const response = await axiosInstance.post('/username/update', { username });\r\n    return response.data;\r\n  } catch (error) {\r\n    console.error('Error updating username:', error);\r\n    throw error;\r\n  }\r\n};\r\n\r\nexport const checkUsernameAvailability = async (username) => {\r\n  try {\r\n    const response = await axiosInstance.post('/username/check-availability', { username });\r\n    return response.data;\r\n  } catch (error) {\r\n    console.error('Error checking username availability:', error);\r\n    throw error;\r\n  }\r\n};\r\n\r\n// Two-Factor Authentication utilities\r\nexport const get2FAStatus = async () => {\r\n  try {\r\n    const response = await axiosInstance.get('/2fa/status');\r\n    return response.data;\r\n  } catch (error) {\r\n    console.error('Error fetching 2FA status:', error);\r\n    throw error;\r\n  }\r\n};\r\n\r\nexport const enable2FA = async () => {\r\n  try {\r\n    const response = await axiosInstance.post('/2fa/enable');\r\n    return response.data;\r\n  } catch (error) {\r\n    console.error('Error enabling 2FA:', error);\r\n    throw error;\r\n  }\r\n};\r\n\r\nexport const disable2FA = async () => {\r\n  try {\r\n    const response = await axiosInstance.post('/2fa/disable');\r\n    return response.data;\r\n  } catch (error) {\r\n    console.error('Error disabling 2FA:', error);\r\n    throw error;\r\n  }\r\n};\r\n\r\nexport const generateRestoreCode = async () => {\r\n  try {\r\n    const response = await axiosInstance.post('/2fa/generate-restore-code');\r\n    return response.data;\r\n  } catch (error) {\r\n    console.error('Error generating restore code:', error);\r\n    throw error;\r\n  }\r\n};\r\n\r\nexport const update2FAAlwaysRequired = async (alwaysRequired) => {\r\n  try {\r\n    const response = await axiosInstance.post('/2fa/update-always-required', {\r\n      always_required: alwaysRequired\r\n    });\r\n    return response.data;\r\n  } catch (error) {\r\n    console.error('Error updating 2FA always required setting:', error);\r\n    throw error;\r\n  }\r\n};\r\n\r\n// Secret Questions utilities\r\nexport const getSecretQuestions = async () => {\r\n  try {\r\n    const response = await axiosInstance.get('/account/secret-questions');\r\n    return response.data;\r\n  } catch (error) {\r\n    console.error('Error fetching secret questions:', error);\r\n    throw error;\r\n  }\r\n};\r\n\r\nexport const saveSecretQuestions = async (questions) => {\r\n  try {\r\n    const response = await axiosInstance.post('/account/secret-questions', { questions });\r\n    return response.data;\r\n  } catch (error) {\r\n    console.error('Error saving secret questions:', error);\r\n    throw error;\r\n  }\r\n};\r\n\r\nexport const updateSecretQuestions = async (questions) => {\r\n  try {\r\n    const response = await axiosInstance.put('/account/secret-questions', { questions });\r\n    return response.data;\r\n  } catch (error) {\r\n    console.error('Error updating secret questions:', error);\r\n    throw error;\r\n  }\r\n};"], "names": [], "mappings": ";;;;;;;;;;;;;;;;;AAAA;AACA;;;AAGO,MAAM,MAAM,OAAO,KAAK,SAAS,CAAC,CAAC;IACxC,IAAI;QACF,MAAM,WAAW,MAAM,6HAAA,CAAA,UAAa,CAAC,GAAG,CAAC,KAAK;YAAE;QAAO;QACvD,OAAO,SAAS,IAAI;IACtB,EAAE,OAAO,OAAO;QACd,IAAI,MAAM,IAAI,KAAK,gBAAgB,qIAAA,CAAA,UAAK,CAAC,QAAQ,CAAC,QAAQ;YACxD,QAAQ,GAAG,CAAC,oBAAoB,MAAM,qBAAqB;YAC3D,OAAO,MAAM,qBAAqB;QACpC;QACA,QAAQ,KAAK,CAAC,2BAA2B;QACzC,MAAM;IACR;AACF;AAEO,MAAM,aAAa,OAAO,KAAK,SAAS,CAAC,CAAC;IAC/C,IAAI;QACF,MAAM,WAAW,MAAM,6HAAA,CAAA,UAAa,CAAC,GAAG,CAAC,KAAK;YAAE;QAAO;QACvD,OAAO,SAAS,IAAI;IACtB,EAAE,OAAO,OAAO;QACd,QAAQ,KAAK,CAAC,2BAA2B;QACzC,MAAM,OAAO,gCAAgC;IAC/C;AACF;AAGO,MAAM,OAAO,OAAO,KAAK;IAC9B,IAAI;QACF,MAAM,WAAW,MAAM,6HAAA,CAAA,UAAa,CAAC,IAAI,CAAC,KAAK;QAC/C,OAAO,SAAS,IAAI;IACtB,EAAE,OAAO,OAAO;QACd,QAAQ,KAAK,CAAC,4BAA4B;QAC1C,MAAM;IACR;AACF;AAGO,MAAM,MAAM,OAAO,KAAK;IAC7B,IAAI;QACF,MAAM,WAAW,MAAM,6HAAA,CAAA,UAAa,CAAC,GAAG,CAAC,KAAK;QAC9C,OAAO,SAAS,IAAI;IACtB,EAAE,OAAO,OAAO;QACd,QAAQ,KAAK,CAAC,2BAA2B;QACzC,MAAM;IACR;AACF;AAGO,MAAM,gBAAgB,OAAO;IAClC,IAAI;QACF,MAAM,WAAW,MAAM,6HAAA,CAAA,UAAa,CAAC,MAAM,CAAC;QAC5C,OAAO,SAAS,IAAI;IACtB,EAAE,OAAO,OAAO;QACd,QAAQ,KAAK,CAAC,8BAA8B;QAC5C,MAAM;IACR;AACF;AAGO,MAAM,iBAAiB,OAAO;IACnC,IAAI;QACF,MAAM,WAAW,MAAM,6HAAA,CAAA,UAAa,CAAC,IAAI,CAAC,oBAAoB;YAAE;QAAS;QACzE,OAAO,SAAS,IAAI;IACtB,EAAE,OAAO,OAAO;QACd,QAAQ,KAAK,CAAC,4BAA4B;QAC1C,MAAM;IACR;AACF;AAEO,MAAM,4BAA4B,OAAO;IAC9C,IAAI;QACF,MAAM,WAAW,MAAM,6HAAA,CAAA,UAAa,CAAC,IAAI,CAAC,gCAAgC;YAAE;QAAS;QACrF,OAAO,SAAS,IAAI;IACtB,EAAE,OAAO,OAAO;QACd,QAAQ,KAAK,CAAC,yCAAyC;QACvD,MAAM;IACR;AACF;AAGO,MAAM,eAAe;IAC1B,IAAI;QACF,MAAM,WAAW,MAAM,6HAAA,CAAA,UAAa,CAAC,GAAG,CAAC;QACzC,OAAO,SAAS,IAAI;IACtB,EAAE,OAAO,OAAO;QACd,QAAQ,KAAK,CAAC,8BAA8B;QAC5C,MAAM;IACR;AACF;AAEO,MAAM,YAAY;IACvB,IAAI;QACF,MAAM,WAAW,MAAM,6HAAA,CAAA,UAAa,CAAC,IAAI,CAAC;QAC1C,OAAO,SAAS,IAAI;IACtB,EAAE,OAAO,OAAO;QACd,QAAQ,KAAK,CAAC,uBAAuB;QACrC,MAAM;IACR;AACF;AAEO,MAAM,aAAa;IACxB,IAAI;QACF,MAAM,WAAW,MAAM,6HAAA,CAAA,UAAa,CAAC,IAAI,CAAC;QAC1C,OAAO,SAAS,IAAI;IACtB,EAAE,OAAO,OAAO;QACd,QAAQ,KAAK,CAAC,wBAAwB;QACtC,MAAM;IACR;AACF;AAEO,MAAM,sBAAsB;IACjC,IAAI;QACF,MAAM,WAAW,MAAM,6HAAA,CAAA,UAAa,CAAC,IAAI,CAAC;QAC1C,OAAO,SAAS,IAAI;IACtB,EAAE,OAAO,OAAO;QACd,QAAQ,KAAK,CAAC,kCAAkC;QAChD,MAAM;IACR;AACF;AAEO,MAAM,0BAA0B,OAAO;IAC5C,IAAI;QACF,MAAM,WAAW,MAAM,6HAAA,CAAA,UAAa,CAAC,IAAI,CAAC,+BAA+B;YACvE,iBAAiB;QACnB;QACA,OAAO,SAAS,IAAI;IACtB,EAAE,OAAO,OAAO;QACd,QAAQ,KAAK,CAAC,+CAA+C;QAC7D,MAAM;IACR;AACF;AAGO,MAAM,qBAAqB;IAChC,IAAI;QACF,MAAM,WAAW,MAAM,6HAAA,CAAA,UAAa,CAAC,GAAG,CAAC;QACzC,OAAO,SAAS,IAAI;IACtB,EAAE,OAAO,OAAO;QACd,QAAQ,KAAK,CAAC,oCAAoC;QAClD,MAAM;IACR;AACF;AAEO,MAAM,sBAAsB,OAAO;IACxC,IAAI;QACF,MAAM,WAAW,MAAM,6HAAA,CAAA,UAAa,CAAC,IAAI,CAAC,6BAA6B;YAAE;QAAU;QACnF,OAAO,SAAS,IAAI;IACtB,EAAE,OAAO,OAAO;QACd,QAAQ,KAAK,CAAC,kCAAkC;QAChD,MAAM;IACR;AACF;AAEO,MAAM,wBAAwB,OAAO;IAC1C,IAAI;QACF,MAAM,WAAW,MAAM,6HAAA,CAAA,UAAa,CAAC,GAAG,CAAC,6BAA6B;YAAE;QAAU;QAClF,OAAO,SAAS,IAAI;IACtB,EAAE,OAAO,OAAO;QACd,QAAQ,KAAK,CAAC,oCAAoC;QAClD,MAAM;IACR;AACF", "debugId": null}}, {"offset": {"line": 648, "column": 0}, "map": {"version": 3, "sources": [], "names": [], "mappings": "A"}}, {"offset": {"line": 654, "column": 0}, "map": {"version": 3, "sources": ["file://C%3A/Users/<USER>/Desktop/Jerrax/Frontend/src/app/%28Home%29/education/page.js"], "sourcesContent": ["import EducationClient from \"./EducationClient\";\r\nimport { generatePageMetadata } from \"@/Seo/Meta/MetadataUtils\";\r\nimport { get } from \"@/utils/apiUtils\";\r\nimport { cookies } from 'next/headers';\r\n\r\n// Generate metadata for Next.js 15 App Router\r\nexport async function generateMetadata({ params, searchParams }) {\r\n  const page = parseInt(params?.id) || 1;\r\n  const cookieStore = cookies();\r\n  const key = cookieStore.get(\"educationSearchKey\")?.value || \"\";\r\n\r\n  const canonicalLink = page === 1\r\n    ? `https://www.tradereply.com/education`\r\n    : `https://www.tradereply.com/education/page/${page}`;\r\n\r\n  const isSearch = key?.trim() !== \"\";\r\n\r\n  const metaArray = {\r\n    title: \"TradeReply Education Center | Learn Trading Strategies\",\r\n    description: \"Learn the essentials of trading with TradeReply.com's Education Center. Access resources and tutorials on trading strategies, market analysis, and more.\",\r\n    og_title: \"TradeReply Education Center | Learn Trading Strategies\",\r\n    og_description: \"Learn the essentials of trading with TradeReply.com's Education Center. Access resources and tutorials on trading strategies, market analysis, and more.\",\r\n    og_site_name: \"TradeReply\",\r\n    twitter_title: \"TradeReply Education Center | Learn Trading Strategies\",\r\n    twitter_description: \"Learn the essentials of trading with TradeReply.com's Education Center. Access resources and tutorials on trading strategies, market analysis, and more.\",\r\n    noindex: isSearch,\r\n    ...(isSearch ? {} : {\r\n      canonical_link: canonicalLink,\r\n    }),\r\n  };\r\n\r\n  return generatePageMetadata(metaArray);\r\n}\r\n\r\nexport default async function EducationPage({ params, searchParams }) {\r\n  // const page = searchParams?.page || 1;\r\n  const page = parseInt(params.id) || 1;\r\n  // const key = searchParams?.key || '';\r\n  const cookieStore = cookies();\r\n  const key = cookieStore.get(\"educationSearchKey\")?.value || \"\";\r\n\r\n  const canonicalLink = page === 1\r\n  ? `https://www.tradereply.com/education`\r\n  : `https://www.tradereply.com/education/page/${page}`;\r\n\r\n  let educationArticles = [];\r\n  let educationPagination = {};\r\n\r\n  let nextLink = null;\r\n\r\n  try {\r\n    const response = await get(\"/article\", {\r\n      key,\r\n      type: \"education\",\r\n      page,\r\n      per_page: 25,\r\n    });\r\n\r\n    educationArticles = response.data.education;\r\n    educationPagination = response.data.meta;\r\n\r\n    if (educationPagination?.current_page < educationPagination?.total) {\r\n      nextLink = `https://www.tradereply.com/education/page/${educationPagination.current_page + 1}`;\r\n    }\r\n  \r\n    console.log(\"educationPagination!!!!!!\", educationPagination);\r\n\r\n\r\n  } catch (error) {\r\n    console.error(\"Failed to fetch education articles:\", error);\r\n  }\r\n   // SEO Meta handling\r\n   const isSearch = key?.trim() !== \"\";\r\n\r\n   const metaArray = {\r\n    title: \"TradeReply Education Center | Learn Trading Strategies\",\r\n    description: \"Learn the essentials of trading with TradeReply.com's Education Center. Access resources and tutorials on trading strategies, market analysis, and more.\",\r\n    og_title: \"TradeReply Education Center | Learn Trading Strategies\",\r\n    og_description: \"Learn the essentials of trading with TradeReply.com's Education Center. Access resources and tutorials on trading strategies, market analysis, and more.\",\r\n    og_site_name: \"TradeReply\",\r\n    twitter_title: \"TradeReply Education Center | Learn Trading Strategies\",\r\n    twitter_description: \"Learn the essentials of trading with TradeReply.com's Education Center. Access resources and tutorials on trading strategies, market analysis, and more.\",\r\n      noindex: isSearch,\r\n      ...(isSearch ? {} : {\r\n        canonical_link: canonicalLink,\r\n      }),\r\n      rel_next: nextLink,\r\n  };\r\n\r\n  return (\r\n    <>\r\n    <EducationClient\r\n      initialArticles={educationArticles}\r\n      initialPagination={educationPagination}\r\n      initialSearch={key}\r\n      initialPage={page}\r\n      metaArray={metaArray} \r\n    />\r\n  </>\r\n  );\r\n}\r\n"], "names": [], "mappings": ";;;;;AAAA;AACA;AACA;AACA;;;;;;AAGO,eAAe,iBAAiB,EAAE,MAAM,EAAE,YAAY,EAAE;IAC7D,MAAM,OAAO,SAAS,QAAQ,OAAO;IACrC,MAAM,cAAc,CAAA,GAAA,+HAAA,CAAA,UAAO,AAAD;IAC1B,MAAM,MAAM,YAAY,GAAG,CAAC,uBAAuB,SAAS;IAE5D,MAAM,gBAAgB,SAAS,IAC3B,CAAC,oCAAoC,CAAC,GACtC,CAAC,0CAA0C,EAAE,MAAM;IAEvD,MAAM,WAAW,KAAK,WAAW;IAEjC,MAAM,YAAY;QAChB,OAAO;QACP,aAAa;QACb,UAAU;QACV,gBAAgB;QAChB,cAAc;QACd,eAAe;QACf,qBAAqB;QACrB,SAAS;QACT,GAAI,WAAW,CAAC,IAAI;YAClB,gBAAgB;QAClB,CAAC;IACH;IAEA,OAAO,CAAA,GAAA,mIAAA,CAAA,uBAAoB,AAAD,EAAE;AAC9B;AAEe,eAAe,cAAc,EAAE,MAAM,EAAE,YAAY,EAAE;IAClE,wCAAwC;IACxC,MAAM,OAAO,SAAS,OAAO,EAAE,KAAK;IACpC,uCAAuC;IACvC,MAAM,cAAc,CAAA,GAAA,+HAAA,CAAA,UAAO,AAAD;IAC1B,MAAM,MAAM,YAAY,GAAG,CAAC,uBAAuB,SAAS;IAE5D,MAAM,gBAAgB,SAAS,IAC7B,CAAC,oCAAoC,CAAC,GACtC,CAAC,0CAA0C,EAAE,MAAM;IAErD,IAAI,oBAAoB,EAAE;IAC1B,IAAI,sBAAsB,CAAC;IAE3B,IAAI,WAAW;IAEf,IAAI;QACF,MAAM,WAAW,MAAM,CAAA,GAAA,wHAAA,CAAA,MAAG,AAAD,EAAE,YAAY;YACrC;YACA,MAAM;YACN;YACA,UAAU;QACZ;QAEA,oBAAoB,SAAS,IAAI,CAAC,SAAS;QAC3C,sBAAsB,SAAS,IAAI,CAAC,IAAI;QAExC,IAAI,qBAAqB,eAAe,qBAAqB,OAAO;YAClE,WAAW,CAAC,0CAA0C,EAAE,oBAAoB,YAAY,GAAG,GAAG;QAChG;QAEA,QAAQ,GAAG,CAAC,6BAA6B;IAG3C,EAAE,OAAO,OAAO;QACd,QAAQ,KAAK,CAAC,uCAAuC;IACvD;IACC,oBAAoB;IACpB,MAAM,WAAW,KAAK,WAAW;IAEjC,MAAM,YAAY;QACjB,OAAO;QACP,aAAa;QACb,UAAU;QACV,gBAAgB;QAChB,cAAc;QACd,eAAe;QACf,qBAAqB;QACnB,SAAS;QACT,GAAI,WAAW,CAAC,IAAI;YAClB,gBAAgB;QAClB,CAAC;QACD,UAAU;IACd;IAEA,qBACE;kBACA,cAAA,8OAAC,sJAAA,CAAA,UAAe;YACd,iBAAiB;YACjB,mBAAmB;YACnB,eAAe;YACf,aAAa;YACb,WAAW;;;;;;;AAIjB", "debugId": null}}, {"offset": {"line": 745, "column": 0}, "map": {"version": 3, "sources": [], "names": [], "mappings": "A"}}]}