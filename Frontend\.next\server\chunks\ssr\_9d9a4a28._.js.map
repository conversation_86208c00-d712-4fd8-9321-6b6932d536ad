{"version": 3, "sources": [], "sections": [{"offset": {"line": 6, "column": 0}, "map": {"version": 3, "sources": [], "names": [], "mappings": "", "debugId": null}}, {"offset": {"line": 6, "column": 0}, "map": {"version": 3, "sources": [], "names": [], "mappings": "A"}}, {"offset": {"line": 24, "column": 0}, "map": {"version": 3, "sources": ["file://C%3A/Users/<USER>/Desktop/Jerrax/Frontend/src/app/%28Home%29/education/%5Bdetail%5D/components/EducationContent.js/proxy.mjs"], "sourcesContent": ["import { registerClientReference } from \"react-server-dom-turbopack/server.edge\";\nexport default registerClientReference(\n    function() { throw new Error(\"Attempted to call the default export of [project]/src/app/(Home)/education/[detail]/components/EducationContent.js <module evaluation> from the server, but it's on the client. It's not possible to invoke a client function from the server, it can only be rendered as a Component or passed to props of a Client Component.\"); },\n    \"[project]/src/app/(Home)/education/[detail]/components/EducationContent.js <module evaluation>\",\n    \"default\",\n);\n"], "names": [], "mappings": ";;;AAAA;;uCACe,CAAA,GAAA,qPAAA,CAAA,0BAAuB,AAAD,EACjC;IAAa,MAAM,IAAI,MAAM;AAAoU,GACjW,kGACA", "debugId": null}}, {"offset": {"line": 32, "column": 0}, "map": {"version": 3, "sources": [], "names": [], "mappings": "A"}}, {"offset": {"line": 38, "column": 0}, "map": {"version": 3, "sources": ["file://C%3A/Users/<USER>/Desktop/Jerrax/Frontend/src/app/%28Home%29/education/%5Bdetail%5D/components/EducationContent.js/proxy.mjs"], "sourcesContent": ["import { registerClientReference } from \"react-server-dom-turbopack/server.edge\";\nexport default registerClientReference(\n    function() { throw new Error(\"Attempted to call the default export of [project]/src/app/(Home)/education/[detail]/components/EducationContent.js from the server, but it's on the client. It's not possible to invoke a client function from the server, it can only be rendered as a Component or passed to props of a Client Component.\"); },\n    \"[project]/src/app/(Home)/education/[detail]/components/EducationContent.js\",\n    \"default\",\n);\n"], "names": [], "mappings": ";;;AAAA;;uCACe,CAAA,GAAA,qPAAA,CAAA,0BAAuB,AAAD,EACjC;IAAa,MAAM,IAAI,MAAM;AAAgT,GAC7U,8EACA", "debugId": null}}, {"offset": {"line": 46, "column": 0}, "map": {"version": 3, "sources": [], "names": [], "mappings": "A"}}, {"offset": {"line": 52, "column": 0}, "map": {"version": 3, "sources": [], "names": [], "mappings": "", "debugId": null}}, {"offset": {"line": 56, "column": 0}, "map": {"version": 3, "sources": [], "names": [], "mappings": "A"}}, {"offset": {"line": 62, "column": 0}, "map": {"version": 3, "sources": ["file://C%3A/Users/<USER>/Desktop/Jerrax/Frontend/src/Layouts/HomeLayout.js/proxy.mjs"], "sourcesContent": ["import { registerClientReference } from \"react-server-dom-turbopack/server.edge\";\nexport default registerClientReference(\n    function() { throw new Error(\"Attempted to call the default export of [project]/src/Layouts/HomeLayout.js <module evaluation> from the server, but it's on the client. It's not possible to invoke a client function from the server, it can only be rendered as a Component or passed to props of a Client Component.\"); },\n    \"[project]/src/Layouts/HomeLayout.js <module evaluation>\",\n    \"default\",\n);\n"], "names": [], "mappings": ";;;AAAA;;uCACe,CAAA,GAAA,qPAAA,CAAA,0BAAuB,AAAD,EACjC;IAAa,MAAM,IAAI,MAAM;AAA6R,GAC1T,2DACA", "debugId": null}}, {"offset": {"line": 70, "column": 0}, "map": {"version": 3, "sources": [], "names": [], "mappings": "A"}}, {"offset": {"line": 76, "column": 0}, "map": {"version": 3, "sources": ["file://C%3A/Users/<USER>/Desktop/Jerrax/Frontend/src/Layouts/HomeLayout.js/proxy.mjs"], "sourcesContent": ["import { registerClientReference } from \"react-server-dom-turbopack/server.edge\";\nexport default registerClientReference(\n    function() { throw new Error(\"Attempted to call the default export of [project]/src/Layouts/HomeLayout.js from the server, but it's on the client. It's not possible to invoke a client function from the server, it can only be rendered as a Component or passed to props of a Client Component.\"); },\n    \"[project]/src/Layouts/HomeLayout.js\",\n    \"default\",\n);\n"], "names": [], "mappings": ";;;AAAA;;uCACe,CAAA,GAAA,qPAAA,CAAA,0BAAuB,AAAD,EACjC;IAAa,MAAM,IAAI,MAAM;AAAyQ,GACtS,uCACA", "debugId": null}}, {"offset": {"line": 84, "column": 0}, "map": {"version": 3, "sources": [], "names": [], "mappings": "A"}}, {"offset": {"line": 90, "column": 0}, "map": {"version": 3, "sources": [], "names": [], "mappings": "", "debugId": null}}, {"offset": {"line": 94, "column": 0}, "map": {"version": 3, "sources": [], "names": [], "mappings": "A"}}, {"offset": {"line": 100, "column": 0}, "map": {"version": 3, "sources": ["file://C%3A/Users/<USER>/Desktop/Jerrax/Frontend/src/Seo/Meta/MetaHead.js"], "sourcesContent": ["\r\n/**\r\n * Next.js 15 App Router Metadata Utilities\r\n *\r\n * This file provides utilities for generating metadata objects that work with\r\n * Next.js 15's metadata API to properly place meta tags and JSON-LD schemas\r\n * in the <head> section via Server-Side Rendering (SSR).\r\n */\r\n\r\n/**\r\n * Generate metadata object for Next.js 15 App Router\r\n * This replaces the old MetaHead component approach\r\n *\r\n * @param {Object} props - Meta properties\r\n * @param {Array} schemas - JSON-LD schemas array\r\n * @returns {Object} - Next.js metadata object\r\n */\r\nexport function generateMetadata(props = {}, schemas = []) {\r\n  const environment = process.env.NEXT_PUBLIC_ENVIRONMENT;\r\n\r\n  const defaultMeta = {\r\n    title: \"TradeReply: Optimize Your Trading Strategies & Analytics\",\r\n    description: \"Optimize your trades with TradeReply.com. Access powerful trading strategies, real-time analytics, and tools for crypto and stock market success.\",\r\n    canonical: \"https://www.tradereply.com/\",\r\n    ogTitle: \"TradeReply: Optimize Your Trading Strategies & Analytics\",\r\n    ogDescription: \"Access powerful trading strategies, real-time analytics, and tools for crypto and stock market success with TradeReply.com.\",\r\n    ogSiteName: \"TradeReply\",\r\n    ogImage: \"https://cdn.tradereply.com/dev/misc/tradereply-trading-analytics-og.png\",\r\n    twitterTitle: \"TradeReply: Optimize Your Trading Strategies & Analytics\",\r\n    twitterDescription: \"Access powerful trading strategies, real-time analytics, and tools for crypto and stock market success with TradeReply.com.\",\r\n    twitterImage: 'https://cdn.tradereply.com/dev/misc/tradereply-trading-analytics-og.png'\r\n  };\r\n\r\n  const isNoIndex = props?.noindex === true;\r\n  const robotsContent = props?.robots || (isNoIndex ? \"noindex, follow\" : \"index, follow\");\r\n\r\n  // Build the metadata object\r\n  const metadata = {\r\n    title: props?.title || defaultMeta.title,\r\n    description: props?.description || defaultMeta.description,\r\n    robots: robotsContent,\r\n\r\n    // Open Graph\r\n    openGraph: {\r\n      title: props?.og_title || defaultMeta.ogTitle,\r\n      description: props?.og_description || defaultMeta.ogDescription,\r\n      siteName: props?.og_site_name || defaultMeta.ogSiteName,\r\n      images: [\r\n        {\r\n          url: defaultMeta.ogImage,\r\n          width: 1200,\r\n          height: 630,\r\n          alt: props?.og_title || defaultMeta.ogTitle,\r\n        },\r\n      ],\r\n      locale: 'en_US',\r\n      type: 'website',\r\n    },\r\n\r\n    // Twitter\r\n    twitter: {\r\n      card: 'summary_large_image',\r\n      title: props?.twitter_title || defaultMeta.twitterTitle,\r\n      description: props?.twitter_description || defaultMeta.twitterDescription,\r\n      images: [defaultMeta.ogImage],\r\n      site: '@JoinTradeReply',\r\n    },\r\n\r\n    // Icons\r\n    icons: {\r\n      icon: [\r\n        {\r\n          url: `https://cdn.tradereply.com/${environment}/site-assets/tradereply-favicon.ico`,\r\n          type: 'image/x-icon',\r\n        },\r\n        {\r\n          url: `https://cdn.tradereply.com/${environment}/site-assets/tradereply-favicon.svg`,\r\n          type: 'image/svg+xml',\r\n        },\r\n      ],\r\n    },\r\n\r\n    // Other metadata\r\n    other: {},\r\n  };\r\n\r\n  // Add canonical URL if provided and not noindex\r\n  if (props?.canonical_link?.trim() && !isNoIndex) {\r\n    metadata.alternates = {\r\n      canonical: props.canonical_link,\r\n    };\r\n  }\r\n\r\n  // Add rel=\"next\" if provided\r\n  if (props?.rel_next) {\r\n    if (!metadata.alternates) metadata.alternates = {};\r\n    metadata.alternates.next = props.rel_next;\r\n  }\r\n\r\n  // Add JSON-LD schemas to the head section\r\n  if (schemas && schemas.length > 0) {\r\n    // Each schema gets its own script tag as per SEO best practices\r\n    schemas.forEach((schema, index) => {\r\n      if (schema) {\r\n        metadata.other[`jsonld-${index}`] = JSON.stringify(schema);\r\n      }\r\n    });\r\n  }\r\n\r\n  return metadata;\r\n}\r\n\r\n/**\r\n * Legacy MetaHead component for backward compatibility\r\n * This component is now deprecated in favor of the generateMetadata function\r\n *\r\n * @deprecated Use generateMetadata() function with Next.js metadata API instead\r\n *\r\n * Migration Guide:\r\n *\r\n * OLD APPROACH (deprecated):\r\n * ```jsx\r\n * import MetaHead from \"@/Seo/Meta/MetaHead\";\r\n * import { generateOrganizationSchema } from \"@/Seo/Schema/JsonLdSchema\";\r\n *\r\n * export default function Page() {\r\n *   const schemas = [generateOrganizationSchema()];\r\n *   return (\r\n *     <>\r\n *       <MetaHead props={metaProps} schemas={schemas} />\r\n *       <div>Page content</div>\r\n *     </>\r\n *   );\r\n * }\r\n * ```\r\n *\r\n * NEW APPROACH (recommended):\r\n * ```jsx\r\n * import { generatePageMetadata } from \"@/Seo/Meta/MetadataUtils\";\r\n * import { generateOrganizationSchema } from \"@/Seo/Schema/JsonLdSchema\";\r\n * import JsonLdHead from \"@/Seo/Schema/JsonLdHead\";\r\n *\r\n * export async function generateMetadata() {\r\n *   return generatePageMetadata(metaProps);\r\n * }\r\n *\r\n * export default function Page() {\r\n *   const schemas = [generateOrganizationSchema()];\r\n *   return (\r\n *     <>\r\n *       <JsonLdHead schemas={schemas} />\r\n *       <div>Page content</div>\r\n *     </>\r\n *   );\r\n * }\r\n * ```\r\n */\r\nexport default function MetaHead({ props, children, schemas }) {\r\n  if (process.env.NODE_ENV === 'development') {\r\n    console.warn(\r\n      'MetaHead component is deprecated. Use generateMetadata() function with Next.js metadata API instead. ' +\r\n      'See the component documentation for migration guide.'\r\n    );\r\n  }\r\n\r\n  // This component should no longer be used in Next.js 15 App Router\r\n  // It's kept for backward compatibility during migration\r\n  return (\r\n    <>\r\n      {children}\r\n      {/* This component no longer renders meta tags - use generateMetadata() instead */}\r\n    </>\r\n  );\r\n}\r\n"], "names": [], "mappings": "AACA;;;;;;CAMC,GAED;;;;;;;CAOC;;;;;;AACM,SAAS,iBAAiB,QAAQ,CAAC,CAAC,EAAE,UAAU,EAAE;IACvD,MAAM;IAEN,MAAM,cAAc;QAClB,OAAO;QACP,aAAa;QACb,WAAW;QACX,SAAS;QACT,eAAe;QACf,YAAY;QACZ,SAAS;QACT,cAAc;QACd,oBAAoB;QACpB,cAAc;IAChB;IAEA,MAAM,YAAY,OAAO,YAAY;IACrC,MAAM,gBAAgB,OAAO,UAAU,CAAC,YAAY,oBAAoB,eAAe;IAEvF,4BAA4B;IAC5B,MAAM,WAAW;QACf,OAAO,OAAO,SAAS,YAAY,KAAK;QACxC,aAAa,OAAO,eAAe,YAAY,WAAW;QAC1D,QAAQ;QAER,aAAa;QACb,WAAW;YACT,OAAO,OAAO,YAAY,YAAY,OAAO;YAC7C,aAAa,OAAO,kBAAkB,YAAY,aAAa;YAC/D,UAAU,OAAO,gBAAgB,YAAY,UAAU;YACvD,QAAQ;gBACN;oBACE,KAAK,YAAY,OAAO;oBACxB,OAAO;oBACP,QAAQ;oBACR,KAAK,OAAO,YAAY,YAAY,OAAO;gBAC7C;aACD;YACD,QAAQ;YACR,MAAM;QACR;QAEA,UAAU;QACV,SAAS;YACP,MAAM;YACN,OAAO,OAAO,iBAAiB,YAAY,YAAY;YACvD,aAAa,OAAO,uBAAuB,YAAY,kBAAkB;YACzE,QAAQ;gBAAC,YAAY,OAAO;aAAC;YAC7B,MAAM;QACR;QAEA,QAAQ;QACR,OAAO;YACL,MAAM;gBACJ;oBACE,KAAK,CAAC,2BAA2B,EAAE,YAAY,mCAAmC,CAAC;oBACnF,MAAM;gBACR;gBACA;oBACE,KAAK,CAAC,2BAA2B,EAAE,YAAY,mCAAmC,CAAC;oBACnF,MAAM;gBACR;aACD;QACH;QAEA,iBAAiB;QACjB,OAAO,CAAC;IACV;IAEA,gDAAgD;IAChD,IAAI,OAAO,gBAAgB,UAAU,CAAC,WAAW;QAC/C,SAAS,UAAU,GAAG;YACpB,WAAW,MAAM,cAAc;QACjC;IACF;IAEA,6BAA6B;IAC7B,IAAI,OAAO,UAAU;QACnB,IAAI,CAAC,SAAS,UAAU,EAAE,SAAS,UAAU,GAAG,CAAC;QACjD,SAAS,UAAU,CAAC,IAAI,GAAG,MAAM,QAAQ;IAC3C;IAEA,0CAA0C;IAC1C,IAAI,WAAW,QAAQ,MAAM,GAAG,GAAG;QACjC,gEAAgE;QAChE,QAAQ,OAAO,CAAC,CAAC,QAAQ;YACvB,IAAI,QAAQ;gBACV,SAAS,KAAK,CAAC,CAAC,OAAO,EAAE,OAAO,CAAC,GAAG,KAAK,SAAS,CAAC;YACrD;QACF;IACF;IAEA,OAAO;AACT;AA+Ce,SAAS,SAAS,EAAE,KAAK,EAAE,QAAQ,EAAE,OAAO,EAAE;IAC3D,wCAA4C;QAC1C,QAAQ,IAAI,CACV,0GACA;IAEJ;IAEA,mEAAmE;IACnE,wDAAwD;IACxD,qBACE;kBACG;;AAIP", "debugId": null}}, {"offset": {"line": 214, "column": 0}, "map": {"version": 3, "sources": [], "names": [], "mappings": "A"}}, {"offset": {"line": 220, "column": 0}, "map": {"version": 3, "sources": ["file://C%3A/Users/<USER>/Desktop/Jerrax/Frontend/src/app/%28Home%29/education/%5Bdetail%5D/page.js"], "sourcesContent": ["import { unstable_noStore as noStore } from 'next/cache';\r\nimport EducationContent from \"./components/EducationContent\";\r\nimport HomeLayout from \"@/Layouts/HomeLayout\";\r\nimport MetaHead from \"@/Seo/Meta/MetaHead\";\r\nimport { Container } from \"react-bootstrap\";\r\nimport Script from \"next/script\"; // <-- add this import\r\n\r\nasync function fetchEducationDetail(detail) {\r\n  const url = new URL(`${process.env.NEXT_PUBLIC_API_BASE_URL}/api/v1/article/education/${detail}`);\r\n\r\n  const res = await fetch(url.toString(), {\r\n    cache: \"no-store\",\r\n  });\r\n\r\n  if (!res.ok) throw new Error(`API error: ${res.status}`);\r\n  return res.json();\r\n}\r\n\r\nexport async function generateMetadata({ params }) {\r\n  noStore();\r\n\r\n  const resolvedParams = await params;\r\n  const detailSlug = resolvedParams.detail;\r\n  const data = await fetchEducationDetail(detailSlug);\r\n  const environment = process.env.NEXT_PUBLIC_ENVIRONMENT;\r\n  return {\r\n    title: `What is ${data.data.title} | TradeReply Education`,\r\n    description: data.data.summary,\r\n    openGraph: {\r\n      title: `What is ${data.data.title} | TradeReply Education`,\r\n      description: data.data.summary,\r\n      images: [{\r\n                url: data?.data?.feature_image_url || 'https://www.tradereply.com/images/tradereply-trading-analytics-og.jpg', // Replace with actual default image if needed\r\n                width: 1200,\r\n                height: 630,\r\n             }],\r\n    },\r\n     twitter: {\r\n         title: `What is ${data?.data?.title} | TradeReply Education`,\r\n         description: data?.data?.summary,\r\n         site: '@JoinTradeReply',\r\n         images: [data?.data?.feature_image_url || 'https://cdn.tradereply.com/dev/misc/tradereply-trading-analytics-og.png'], // Replace with actual default image if needed\r\n       },\r\n       icons: {\r\n                icon: [\r\n                  {\r\n                    url: `https://cdn.tradereply.com/${environment}/site-assets/tradereply-favicon.ico`,\r\n                    type: \"image/x-icon\",\r\n                  },\r\n                  {\r\n                    url: `https://cdn.tradereply.com/${environment}/site-assets/tradereply-favicon.svg`,\r\n                    type: \"image/svg+xml\",\r\n                  },\r\n                ],\r\n              },\r\n  };\r\n}\r\n\r\nexport default async function EducationDetail({ params }) {\r\n  noStore();\r\n\r\n  const resolvedParams = await params;\r\n  const detailSlug = resolvedParams.detail;\r\n\r\n  const data = await fetchEducationDetail(detailSlug);\r\n  const articleData = data.data;\r\n\r\n\r\n  return (\r\n    <HomeLayout>\r\n      <Container>\r\n        {/* JSON-LD Article Schema Only */}\r\n        <Script id=\"ld-json-article\" type=\"application/ld+json\">\r\n          {JSON.stringify({\r\n            \"@context\": \"https://schema.org\",\r\n            \"@type\": \"Article\",\r\n            \"mainEntityOfPage\": {\r\n              \"@type\": \"WebPage\",\r\n              \"@id\": `https://www.tradereply.com/education/${detailSlug}`\r\n            },\r\n            \"headline\": articleData.title,\r\n            \"description\": articleData.summary,\r\n            \"author\": {\r\n              \"@type\": \"Organization\",\r\n              \"name\": \"TradeReply\"\r\n            },\r\n            \"publisher\": {\r\n              \"@type\": \"Organization\",\r\n              \"name\": \"TradeReply\",\r\n              \"logo\": {\r\n                \"@type\": \"ImageObject\",\r\n                \"url\": \"https://cdn.tradereply.com/main/misc/tradereply-public-logo-search.png\"\r\n              }\r\n            },\r\n            \"datePublished\": new Date(articleData.created_at).toISOString(),\r\n            \"dateModified\": new Date(articleData.updated_at || articleData.created_at).toISOString(),\r\n            \"articleSection\": \"Education\",\r\n            \"articleBody\": articleData.articleBody || \"\"\r\n          })}\r\n        </Script>\r\n        <EducationContent\r\n          detailSlug={detailSlug}\r\n          articleData={data.data}\r\n          nextArticle={data.next_article}\r\n          avgProgress={data.avgProgress}\r\n        />\r\n      </Container>\r\n    </HomeLayout>\r\n  );\r\n}"], "names": [], "mappings": ";;;;;AAAA;AACA;AACA;AACA;AACA;AACA,8NAAkC,sBAAsB;;;;;;;;AAExD,eAAe,qBAAqB,MAAM;IACxC,MAAM,MAAM,IAAI,IAAI,6DAAwC,0BAA0B,EAAE,QAAQ;IAEhG,MAAM,MAAM,MAAM,MAAM,IAAI,QAAQ,IAAI;QACtC,OAAO;IACT;IAEA,IAAI,CAAC,IAAI,EAAE,EAAE,MAAM,IAAI,MAAM,CAAC,WAAW,EAAE,IAAI,MAAM,EAAE;IACvD,OAAO,IAAI,IAAI;AACjB;AAEO,eAAe,iBAAiB,EAAE,MAAM,EAAE;IAC/C,CAAA,GAAA,6HAAA,CAAA,mBAAO,AAAD;IAEN,MAAM,iBAAiB,MAAM;IAC7B,MAAM,aAAa,eAAe,MAAM;IACxC,MAAM,OAAO,MAAM,qBAAqB;IACxC,MAAM;IACN,OAAO;QACL,OAAO,CAAC,QAAQ,EAAE,KAAK,IAAI,CAAC,KAAK,CAAC,uBAAuB,CAAC;QAC1D,aAAa,KAAK,IAAI,CAAC,OAAO;QAC9B,WAAW;YACT,OAAO,CAAC,QAAQ,EAAE,KAAK,IAAI,CAAC,KAAK,CAAC,uBAAuB,CAAC;YAC1D,aAAa,KAAK,IAAI,CAAC,OAAO;YAC9B,QAAQ;gBAAC;oBACC,KAAK,MAAM,MAAM,qBAAqB;oBACtC,OAAO;oBACP,QAAQ;gBACX;aAAE;QACX;QACC,SAAS;YACL,OAAO,CAAC,QAAQ,EAAE,MAAM,MAAM,MAAM,uBAAuB,CAAC;YAC5D,aAAa,MAAM,MAAM;YACzB,MAAM;YACN,QAAQ;gBAAC,MAAM,MAAM,qBAAqB;aAA0E;QACtH;QACA,OAAO;YACE,MAAM;gBACJ;oBACE,KAAK,CAAC,2BAA2B,EAAE,YAAY,mCAAmC,CAAC;oBACnF,MAAM;gBACR;gBACA;oBACE,KAAK,CAAC,2BAA2B,EAAE,YAAY,mCAAmC,CAAC;oBACnF,MAAM;gBACR;aACD;QACH;IACZ;AACF;AAEe,eAAe,gBAAgB,EAAE,MAAM,EAAE;IACtD,CAAA,GAAA,6HAAA,CAAA,mBAAO,AAAD;IAEN,MAAM,iBAAiB,MAAM;IAC7B,MAAM,aAAa,eAAe,MAAM;IAExC,MAAM,OAAO,MAAM,qBAAqB;IACxC,MAAM,cAAc,KAAK,IAAI;IAG7B,qBACE,8OAAC,4HAAA,CAAA,UAAU;kBACT,cAAA,8OAAC,8LAAA,CAAA,YAAS;;8BAER,8OAAC,8HAAA,CAAA,UAAM;oBAAC,IAAG;oBAAkB,MAAK;8BAC/B,KAAK,SAAS,CAAC;wBACd,YAAY;wBACZ,SAAS;wBACT,oBAAoB;4BAClB,SAAS;4BACT,OAAO,CAAC,qCAAqC,EAAE,YAAY;wBAC7D;wBACA,YAAY,YAAY,KAAK;wBAC7B,eAAe,YAAY,OAAO;wBAClC,UAAU;4BACR,SAAS;4BACT,QAAQ;wBACV;wBACA,aAAa;4BACX,SAAS;4BACT,QAAQ;4BACR,QAAQ;gCACN,SAAS;gCACT,OAAO;4BACT;wBACF;wBACA,iBAAiB,IAAI,KAAK,YAAY,UAAU,EAAE,WAAW;wBAC7D,gBAAgB,IAAI,KAAK,YAAY,UAAU,IAAI,YAAY,UAAU,EAAE,WAAW;wBACtF,kBAAkB;wBAClB,eAAe,YAAY,WAAW,IAAI;oBAC5C;;;;;;8BAEF,8OAAC,mLAAA,CAAA,UAAgB;oBACf,YAAY;oBACZ,aAAa,KAAK,IAAI;oBACtB,aAAa,KAAK,YAAY;oBAC9B,aAAa,KAAK,WAAW;;;;;;;;;;;;;;;;;AAKvC", "debugId": null}}, {"offset": {"line": 353, "column": 0}, "map": {"version": 3, "sources": [], "names": [], "mappings": "A"}}]}