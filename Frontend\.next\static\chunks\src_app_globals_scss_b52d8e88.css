/* [project]/src/app/globals.scss.css [app-client] (css) */
:root {
  --font-g<PERSON>roy: "<PERSON><PERSON>", sans-serif;
}

body {
  font-size: 1rem;
  overflow-x: clip;
  background-color: #011132;
  font-family: var(--font-gilroy);
}

*, :after, :before {
  box-sizing: border-box;
}

div.__toast, .react-hot-toast, [data-sonner-toast], [data-toast] {
  z-index: 10000 !important;
}

hr {
  color: #fff;
}

img {
  max-width: 100%;
}

ul {
  padding: 0;
  margin: 0;
  list-style: none;
}

a, a:hover {
  text-decoration: none;
  transition: all .3s ease-in-out;
  color: #00adef;
}

.text-link {
  color: #00adef;
}

.text-link:hover {
  opacity: .6;
}

a, span {
  display: inline-block;
}

svg path {
  transition: all .3s ease-in-out;
}

ol {
  padding: 0;
  margin: 0;
}

small {
  font-family: <PERSON><PERSON>, "sans-serif";
}

h1, .h1, h2, .h2, h3, .h3, h4, .h4, h5, .h5, h6, .h6, p {
  margin-bottom: 0;
  color: #fff;
}

h1, .h1 {
  font-size: 3rem;
  font-weight: 800;
}

@media (width <= 1199px) {
  h1, .h1 {
    font-size: 2.5rem;
  }
}

@media (width <= 767px) {
  h1, .h1 {
    font-size: 1.5rem;
  }
}

@media (width <= 390px) {
  h1, .h1 {
    font-size: 1.3rem;
  }
}

h2, .h2 {
  font-size: 5rem;
  font-weight: 800;
}

@media (width <= 1269px) {
  h2, .h2 {
    font-size: 3rem;
  }
}

@media (width <= 767px) {
  h2, .h2 {
    font-size: 1.363rem;
  }
}

h3, .h3 {
  font-size: 2.8rem;
  font-weight: 800;
}

@media (width <= 1199px) {
  h3, .h3 {
    font-size: 1.688rem;
  }
}

@media (width <= 767px) {
  h3, .h3 {
    font-size: 1.25rem;
  }
}

h4, .h4 {
  font-size: 1.65rem;
  line-height: 35px;
  font-weight: 600;
}

@media (width <= 767px) {
  h4, .h4 {
    font-size: 1.15rem;
    line-height: 25px;
  }
}

h5, .h5 {
  font-size: 1.25rem;
  line-height: 30px;
  font-weight: 600;
}

@media (width <= 767px) {
  h5, .h5 {
    font-size: 1rem;
    line-height: 25px;
  }
}

h6, .h6 {
  font-size: 1.125rem;
  font-weight: 600;
}

@media (width <= 767px) {
  h6, .h6 {
    font-size: 1rem;
  }
}

p {
  font-size: 1rem;
  font-weight: 400;
}

@media (width <= 767px) {
  p {
    font-size: .875rem;
  }
}

.font-weight-400 {
  font-weight: 400;
}

.font-weight-500 {
  font-weight: 500;
}

.font-weight-600 {
  font-weight: 600;
}

.font-weight-700 {
  font-weight: 700;
}

.font-weight-800 {
  font-weight: 800;
}

.divider {
  height: 1px;
  width: 100%;
  background-color: #666;
  opacity: 1;
  margin: 1.25rem 0;
}

.pt-40 {
  padding-top: 40px;
}

.pt-50 {
  padding-top: 50px;
}

.pt-70 {
  padding-top: 70px;
}

.pb-40 {
  padding-bottom: 40px;
}

.pb-50 {
  padding-bottom: 50px;
}

.pb-60 {
  padding-bottom: 60px;
}

.pb-70 {
  padding-bottom: 70px;
}

.py-40 {
  padding: 40px 0;
}

.py-80 {
  padding: 80px 0;
}

@media (width <= 767px) {
  .py-80 {
    padding: 40px 0;
  }
}

.py-100 {
  padding: 100px 0;
}

@media (width <= 1199px) {
  .py-100 {
    padding: 70px 0 !important;
  }
}

@media (width <= 767px) {
  .py-100 {
    padding: 50px 0 !important;
  }
}

.mt-10 {
  margin-top: 10px !important;
}

.mt-20 {
  margin-top: 20px !important;
}

.mt-30 {
  margin-top: 30px !important;
}

.mt-40 {
  margin-top: 40px !important;
}

@media (width <= 767px) {
  .mt-40 {
    margin-top: 30px !important;
  }
}

.mt-50 {
  margin-top: 50px !important;
}

@media (width <= 767px) {
  .mt-50 {
    margin-top: 30px !important;
  }
}

.my-10 {
  margin: 10px 0 !important;
}

.my-20 {
  margin: 20px 0 !important;
}

.my-30 {
  margin: 30px 0 !important;
}

.my-40 {
  margin: 40px 0 !important;
}

@media (width <= 767px) {
  .my-40 {
    margin: 30px 0 !important;
  }
}

.my-50 {
  margin: 50px 0 !important;
}

@media (width <= 767px) {
  .my-50 {
    margin: 30px 0 !important;
  }
}

figure {
  margin-bottom: 0;
}

.black_text {
  color: #000;
}

.white_text {
  color: #fff;
}

.red_text {
  color: #ff0302 !important;
}

.red_text svg path {
  fill: #ff0302 !important;
}

.green_text {
  color: #32cd33 !important;
}

.green_text svg path {
  fill: #32cd33 !important;
}

.gray_text {
  color: #9c9a9f !important;
}

.gray_text svg path {
  fill: #9c9a9f !important;
}

.white_icon svg path {
  fill: #fff !important;
}

.yellowlight_text {
  color: #feff14 !important;
}

.greenlight_text {
  color: #7aff67 !important;
}

.redlight_text {
  color: #d54d3f !important;
}

.darkblue_text {
  color: #04498c !important;
}

.blue_text {
  color: #00adef !important;
}

.grey_text {
  color: #c5c5d5 !important;
}

.lightgrey_text {
  color: #c5c5c5 !important;
}

.darkgrey_text {
  color: gray !important;
}

.yellow_text {
  color: #fea500 !important;
}

.yellow_text svg path {
  fill: #fea500 !important;
}

.green_bg {
  background-color: #7af870 !important;
}

.red_bg {
  background-color: #ff696a !important;
}

.blue_bg {
  background-color: #031940 !important;
}

.baseblue_bg {
  background-color: #3791d3 !important;
  border-radius: 1.25rem !important;
}

.baseblue_bg .solidArrow svg path {
  fill: #00adef !important;
}

.baseblue_bg:hover {
  background: linear-gradient(0deg, #ffffff28, #ffffff28), linear-gradient(270.33deg, #00adef66, #00adef26 45.5%, #00adef66 98%) !important;
}

.bluelight_bg {
  background-color: #04498c !important;
  color: #fff !important;
}

.greenlight_bg {
  background-color: #7af870 !important;
}

.white_bg {
  background-color: #fff !important;
}

.whitelight_bg {
  background-color: #fff3 !important;
}

.Redgrandient {
  background: linear-gradient(0deg, #ffffff1a, #ffffff1a), linear-gradient(270.33deg, #ff696a66 0%, #ff696a33 50%, #ff696a66 100%) !important;
}

.Redgrandient svg path {
  fill: #ff696a !important;
}

.Redgrandient:hover {
  background: linear-gradient(0deg, #ffffff28, #ffffff28), linear-gradient(270.33deg, #ff696a80, #ff696a40, #ff696a80) !important;
}

.greengrandient {
  background: linear-gradient(0deg, #ffffff1a, #ffffff1a), linear-gradient(270.33deg, #32cd3366 0%, #32cd3326 45.5%, #32cd3366 98%) !important;
}

.greengrandient svg path {
  fill: #32cd33 !important;
}

.greengrandient:hover {
  background: linear-gradient(0deg, #ffffff28, #ffffff28), linear-gradient(270.33deg, #32cd3380, #32cd3340 45.5%, #32cd3380 98%) !important;
}

.bluegrandient {
  background: linear-gradient(0deg, #ffffff1a, #ffffff1a), linear-gradient(270.33deg, #00adef66 0%, #00adef26 45.5%, #00adef66 98%) !important;
  border-radius: 1.25rem !important;
}

.bluegrandient .solidArrow svg path {
  fill: #00adef !important;
}

.bluegrandient:hover {
  background: linear-gradient(0deg, #ffffff28, #ffffff28), linear-gradient(270.33deg, #00adef66, #00adef26 45.5%, #00adef66 98%) !important;
}

.greengrandientbg {
  border-radius: 1.25rem !important;
  background: linear-gradient(0deg, #ffffff1a, #ffffff1a), linear-gradient(270.33deg, #32cd3366 0%, #32cd3326 45.5%, #32cd3366 98%) !important;
  border: 0 !important;
}

.redgrandientbg {
  border-radius: 1.25rem !important;
  background: linear-gradient(0deg, #ffffff1a, #ffffff1a), linear-gradient(270.33deg, #ff696a66 0%, #ff696a33 50%, #ff696a66 100%) !important;
  border: 0 !important;
}

.bluedark_bg {
  background: #04498c !important;
}

.cardgrandient {
  background: radial-gradient(50% 50%, #00b9ff80 21.5%, #00539980 100%), linear-gradient(135deg, #fff0 0%, #fff3 47.5%, #fff0 100%);
}

.green_arrow svg path {
  fill: #32cd33 !important;
}

body ::-webkit-scrollbar {
  width: 5px;
  height: 4px;
  border-radius: 1rem;
}

body ::-webkit-scrollbar-track {
  box-shadow: none;
}

body ::-webkit-scrollbar-thumb {
  background-color: #00adef;
  border-radius: 1rem;
}

.container {
  max-width: 1200px;
  margin: 0 auto;
  padding-left: 20px !important;
  padding-right: 20px !important;
}

@media (width >= 1400px) {
  .container {
    max-width: 1300px;
  }
}

@media (width <= 767px) {
  .container {
    padding-left: 15px;
    padding-right: 15px;
  }
}

input:-webkit-autofill, input:-webkit-autofill:hover, input:-webkit-autofill:focus, input:-webkit-autofill:active {
  transition: background-color 5000s ease-in-out;
  -webkit-text-fill-color: #000;
  caret-color: #0000 !important;
}

.commonCard {
  background-color: #9f9f9f1a;
  padding: 2.5em;
  border-radius: .625rem;
  border: 1px solid #00adef;
}

@media (width <= 1399px) {
  .commonCard {
    padding: 2em;
  }
}

@media (width <= 1199px) {
  .commonCard {
    padding: 1.25em 1rem;
  }
}

.borderTabs {
  white-space: nowrap;
  flex-wrap: nowrap;
  overflow-x: auto;
  overflow-y: clip;
  border-bottom: 0;
}

.borderTabs.nav {
  border-bottom: 1px solid #00adef;
}

.borderTabs.nav .nav-item .nav-link {
  color: #00adef;
  font-size: 1rem;
  font-weight: 600;
  line-height: normal;
  background-color: #0000;
  border: 0;
  border-bottom: 3px solid #0000;
  border-radius: 0;
  padding: 0 1.5rem 1rem;
  transition: all .3s ease-in-out;
}

@media (width <= 767px) {
  .borderTabs.nav .nav-item .nav-link {
    font-size: 1rem;
  }
}

.borderTabs.nav .nav-item .nav-link.active {
  border-bottom: 5px solid #00adef;
  color: #fff;
}

.radioBtn {
  display: flex;
  flex-wrap: wrap;
}

.radioBtn .checkbox_input .form-check {
  display: flex;
  align-items: center;
  justify-content: center;
  border: 1px solid #666;
  border-radius: 1rem;
  min-height: 64px;
  padding: .5rem 1.25rem;
  cursor: pointer;
  transition: all .3s ease-in-out;
}

@media (width <= 1399px) {
  .radioBtn .checkbox_input .form-check {
    min-height: 50px;
  }
}

@media (width <= 767px) {
  .radioBtn .checkbox_input .form-check {
    padding: .5rem 1rem;
  }
}

.radioBtn .checkbox_input .form-check .form-check-input {
  margin: 0;
  cursor: pointer;
  background-color: #0000;
  width: 20px !important;
  height: 20px !important;
}

.radioBtn .checkbox_input .form-check .form-check-label {
  margin-bottom: 0;
  cursor: pointer;
  display: flex;
  align-items: center;
  padding-left: 1.25rem;
  margin: 0;
  color: #9c9a9f;
}

@media (width <= 575px) {
  .radioBtn .checkbox_input .form-check .form-check-label {
    padding-left: .5rem;
  }
}

.radioBtn .checkbox_input .form-check .form-check-label .radioIcon {
  margin-right: .625rem;
}

.radioBtn .checkbox_input .form-check.active, .radioBtn .checkbox_input .form-check:hover {
  background-color: #160125;
  border-color: #00adef;
}

.radioBtn .checkbox_input .form-check.active .form-check-label, .radioBtn .checkbox_input .form-check:hover .form-check-label {
  color: #c5c5d5;
}

.big_tabs.nav .nav-item {
  display: flex;
}

.big_tabs.nav .nav-item .nav-link {
  padding: 1.5rem 1rem;
  background-color: #04498c33;
  border: 2px solid #04498c33;
  width: 100%;
  border-radius: 30px;
  text-align: center;
  font-size: 24px;
  font-weight: 700;
  letter-spacing: -1px;
  display: flex;
  align-items: center;
  justify-content: center;
  flex-direction: column;
  color: #fff;
}

@media (width <= 767px) {
  .big_tabs.nav .nav-item .nav-link {
    font-size: 18px;
    padding: 1rem .5rem;
  }
}

.big_tabs.nav .nav-item .nav-link .tabs_icon {
  display: block;
  width: 56px;
  height: 56px;
  background-color: #0319404d;
  border-radius: 50%;
  display: flex;
  align-items: center;
  justify-content: center;
  margin-bottom: 14px;
}

.big_tabs.nav .nav-item .nav-link.active, .big_tabs.nav .nav-item .nav-link:hover, .big_tabs.nav .nav-item .nav-link:focus {
  border: 2px solid #00adef80;
  color: #fff;
  background: linear-gradient(#04498c 0%, #011426 100%) !important;
}

.slider-container .slick-slider .slick-arrow.slick-prev, .slider-container .slick-slider .slick-arrow.slick-next {
  position: absolute;
  bottom: 0;
  width: 50px;
  height: 50px;
  border-radius: 10rem;
  background-color: #00adef;
  z-index: 2;
}

@media (width <= 991px) {
  .slider-container .slick-slider .slick-arrow.slick-prev, .slider-container .slick-slider .slick-arrow.slick-next {
    width: 32px;
    height: 32px;
  }
}

.slider-container .slick-slider .slick-arrow.slick-prev {
  left: -25px;
}

@media (width >= 576px) and (width <= 767px) {
  .slider-container .slick-slider .slick-arrow.slick-prev {
    left: 5px;
  }
}

@media (width <= 374px) {
  .slider-container .slick-slider .slick-arrow.slick-prev {
    left: 5px;
  }
}

.slider-container .slick-slider .slick-arrow.slick-prev:before {
  content: "";
  width: 8px;
  height: 15px;
  background-image: url("https: //cdn.tradereply.com/dev/site-assets/icons/tradereply-right-arrow.svg");
  background-repeat: no-repeat;
  background-size: 100%;
  position: absolute;
  top: 50%;
  left: 48%;
  transform: translate(-50%, -50%)rotate(180deg);
  opacity: 1;
}

.slider-container .slick-slider .slick-arrow.slick-next {
  right: -25px;
}

@media (width >= 576px) and (width <= 767px) {
  .slider-container .slick-slider .slick-arrow.slick-next {
    right: 5px;
  }
}

@media (width <= 374px) {
  .slider-container .slick-slider .slick-arrow.slick-next {
    right: 5px;
  }
}

.slider-container .slick-slider .slick-arrow.slick-next:before {
  content: "";
  width: 8px;
  height: 15px;
  background-image: url("https: //cdn.tradereply.com/dev/site-assets/icons/tradereply-right-arrow.svg");
  background-repeat: no-repeat;
  background-size: 100%;
  position: absolute;
  top: 54%;
  left: 52%;
  transform: translate(-50%, -50%);
  opacity: 1;
}

.common_dropdown.dropdown .dropdown-toggle {
  color: #fff;
  border: 0;
  border-radius: .625rem;
  font-size: 1.25rem;
  padding: .625rem 1.25rem;
  display: flex;
  align-items: center;
}

@media (width <= 991px) {
  .common_dropdown.dropdown .dropdown-toggle {
    font-size: 1.8rem;
  }
}

@media (width >= 1200px) {
  .common_dropdown.dropdown .dropdown-toggle:hover {
    background-color: #283f67;
    color: #fff;
  }
}

.common_dropdown.dropdown .dropdown-menu {
  background-color: #031940;
  border-radius: .625rem;
  border: 1px solid #ffffff4d;
  min-width: 200px;
}

.common_dropdown.dropdown .dropdown-menu .dropdown-item {
  font-size: 1.25rem;
  font-weight: 600;
  padding: .625rem 1rem;
  color: #fff;
}

.common_dropdown.dropdown .dropdown-menu .dropdown-item:hover {
  background-color: #283f67;
  color: #fff;
}

@media (width <= 991px) {
  .common_dropdown.dropdown .dropdown-menu .dropdown-item {
    font-size: 1rem;
  }
}

.home-page .common_dropdown.dropdown .dropdown-menu {
  background-color: #1e222d;
}

.home-page .common_dropdown.dropdown .dropdown-menu .dropdown-item:hover {
  background-color: #2a2e39;
  color: #fff;
}

.form-control {
  min-height: 56px;
  box-shadow: none;
  outline: none;
  width: 100%;
  padding: .5rem 1.25rem;
  border-radius: 1rem;
  border: 1px solid #ffffff4d;
  background-color: #ffffff4d;
  color: #fff;
  font-size: 1rem;
}

@media (width <= 1599px) {
  .form-control {
    min-height: 52px;
    font-size: 1rem;
  }
}

.form-control.is-invalid, .form-control.was-validated, .form-control:invalid {
  background-image: none;
  border-color: #ff0302;
}

.form-control:hover {
  appearance: none;
}

.form-control::placeholder {
  color: #fff;
  opacity: .4;
}

.form-control:disabled {
  background-color: #0000;
}

.form-control:focus {
  box-shadow: none;
  border: 1px solid #ffffff4d;
  background-color: #ffffff4d;
  color: #fff;
}

.form-control.passwordInput {
  padding-right: 4.375rem;
}

.login_fontStyle_forget {
  font-weight: 700;
}

.text_area_bg {
  width: 100%;
  height: 15rem;
  border-radius: 15px;
  border: 1px solid #ffffff4d;
  background-color: #ffffff4d;
}

.text-pre-line {
  white-space: pre-line;
}

.select-btn {
  background: #136fcb;
  width: 50%;
  margin: 10px 0;
  border-radius: 7px !important;
}

.green_btn {
  color: #fff;
  background-color: #32cd33 !important;
}

.green_btn:hover {
  background-color: #2bb72b !important;
}

.white_btn {
  background-color: #fff !important;
  color: #000 !important;
  border: 1px solid #0003 !important;
}

.yellow_btn {
  color: #fff;
  background-color: #fea500 !important;
}

.password_check p {
  font-size: 14px;
  text-align: center;
}

.password_check .box1 p {
  color: #ff696a;
  font-weight: 600;
}

.password_check .box1_bg {
  background-color: #ff696a;
}

.password_check .box2_bg {
  background-color: #ff6a23;
}

.password_check .box3_bg {
  background-color: #ffa723;
}

.password_check .box4_bg {
  background-color: #fcd53f;
}

.password_check .box5_bg {
  background-color: #dff33b;
}

.password_check .white10_bg {
  background-color: #ffffff10;
}

.security_check .user_email {
  color: #32cd33;
  font-size: 18px;
  font-weight: 600;
}

.security_check_input input {
  height: 57px;
  width: 58px;
  border-radius: 15px;
  background-color: #ffffff30;
  text-align: center;
  font-size: 30px;
}

.security_check_input input:focus-visible {
  outline: 1px solid #fff !important;
}

.security_check_resend_btn {
  background-color: #031940;
  padding: 10px 50px;
  border-radius: 50px;
  transition: all .3s ease-in-out;
  font-weight: 600;
  font-size: 20px;
}

.security_check_resend_btn:hover {
  background-color: #0099d1;
  color: #fff;
}

.security_check_resend_btn:disabled, .security_check_resend_btn:disabled:hover {
  background-color: #6c757d;
  cursor: not-allowed;
}

.rotate {
  animation: 1s linear spin;
}

@keyframes spin {
  0% {
    transform: rotate(0);
  }

  100% {
    transform: rotate(360deg);
  }
}

.svg-baseblue {
  filter: invert(49%) sepia(63%) saturate(5181%) hue-rotate(171deg) brightness(96%) contrast(97%);
  width: 33px;
  height: 32px;
}

.baseblue_border {
  border-color: #00adef !important;
}

.darkblue_border {
  border-color: #04498c !important;
}

.darkgray_border {
  border-color: gray !important;
}

.portfolio-blur-overlay {
  position: fixed;
  z-index: 99999;
  inset: 0;
  background-color: #ffffff80;
  backdrop-filter: blur(3px);
  display: flex;
  align-items: center;
  justify-content: center;
}

.portfolio-blur-overlay .loader-content {
  text-align: center;
  font-weight: bold;
  color: #1338ff;
}

.portfolio-blur-overlay .loader-content .spinner-border {
  width: 2.5rem;
  height: 2.5rem;
}

.loading-screen {
  display: flex;
  justify-content: center;
  align-items: center;
  height: 100vh;
}

@font-face {
  font-family: Gilroy;
  src: url("https://cdn.tradereply.com/dev/site-assets/fonts/Gilroy-Regular.woff") format("woff");
  src: url("https://cdn.tradereply.com/dev/site-assets/fonts/Gilroy-Regular.woff2") format("woff2");
  font-weight: 400;
  font-style: normal;
}

@font-face {
  font-family: Gilroy;
  src: url("https://cdn.tradereply.com/dev/site-assets/fonts/Gilroy-Bold.woff") format("woff");
  src: url("https://cdn.tradereply.com/dev/site-assets/fonts/Gilroy-Bold.woff2") format("woff2");
  font-weight: 700;
  font-style: normal;
}

@font-face {
  font-family: Gilroy-Bold;
  src: url("https://cdn.tradereply.com/dev/site-assets/fonts/Gilroy-Bold.woff") format("woff");
  src: url("https://cdn.tradereply.com/dev/site-assets/fonts/Gilroy-Bold.woff2") format("woff2");
  font-weight: 400;
  font-style: normal;
}

@font-face {
  font-family: Gilroy;
  src: url("https://cdn.tradereply.com/dev/site-assets/fonts/Gilroy-Extrabold.woff") format("woff");
  src: url("https://cdn.tradereply.com/dev/site-assets/fonts/Gilroy-Extrabold.woff2") format("woff2");
  font-weight: 900;
  font-style: normal;
}

@font-face {
  font-family: Gilroy;
  src: url("https://cdn.tradereply.com/dev/site-assets/fonts/Gilroy-Medium.woff") format("woff");
  src: url("https://cdn.tradereply.com/dev/site-assets/fonts/Gilroy-Medium.woff2") format("woff2");
  font-weight: 500;
  font-style: normal;
}

@font-face {
  font-family: Gilroy;
  src: url("https://cdn.tradereply.com/dev/site-assets/fonts/Gilroy-LightItalic.woff") format("woff");
  src: url("https://cdn.tradereply.com/dev/site-assets/fonts/Gilroy-LightItalic.woff2") format("woff2");
  font-weight: 400;
  font-style: italic;
}

@font-face {
  font-family: Gilroy;
  src: url("https://cdn.tradereply.com/dev/site-assets/fonts/Gilroy-BoldItalic.woff") format("woff");
  src: url("https://cdn.tradereply.com/dev/site-assets/fonts/Gilroy-BoldItalic.woff2") format("woff2");
  font-weight: 700;
  font-style: italic;
}

@font-face {
  font-family: Gilroy-Semibold;
  src: url("https://cdn.tradereply.com/dev/site-assets/fonts/Gilroy-Semibold.woff") format("woff");
  src: url("https://cdn.tradereply.com/dev/site-assets/fonts/Gilroy-Semibold.woff2") format("woff2");
  font-weight: 400;
  font-style: normal;
}

:root {
  --font-gilroy: "Gilroy", sans-serif;
}

*, :before, :after {
  --tw-border-spacing-x: 0;
  --tw-border-spacing-y: 0;
  --tw-translate-x: 0;
  --tw-translate-y: 0;
  --tw-rotate: 0;
  --tw-skew-x: 0;
  --tw-skew-y: 0;
  --tw-scale-x: 1;
  --tw-scale-y: 1;
  --tw-pan-x: ;
  --tw-pan-y: ;
  --tw-pinch-zoom: ;
  --tw-scroll-snap-strictness: proximity;
  --tw-gradient-from-position: ;
  --tw-gradient-via-position: ;
  --tw-gradient-to-position: ;
  --tw-ordinal: ;
  --tw-slashed-zero: ;
  --tw-numeric-figure: ;
  --tw-numeric-spacing: ;
  --tw-numeric-fraction: ;
  --tw-ring-inset: ;
  --tw-ring-offset-width: 0px;
  --tw-ring-offset-color: #fff;
  --tw-ring-color: #3b82f680;
  --tw-ring-offset-shadow: 0 0 #0000;
  --tw-ring-shadow: 0 0 #0000;
  --tw-shadow: 0 0 #0000;
  --tw-shadow-colored: 0 0 #0000;
  --tw-blur: ;
  --tw-brightness: ;
  --tw-contrast: ;
  --tw-grayscale: ;
  --tw-hue-rotate: ;
  --tw-invert: ;
  --tw-saturate: ;
  --tw-sepia: ;
  --tw-drop-shadow: ;
  --tw-backdrop-blur: ;
  --tw-backdrop-brightness: ;
  --tw-backdrop-contrast: ;
  --tw-backdrop-grayscale: ;
  --tw-backdrop-hue-rotate: ;
  --tw-backdrop-invert: ;
  --tw-backdrop-opacity: ;
  --tw-backdrop-saturate: ;
  --tw-backdrop-sepia: ;
  --tw-contain-size: ;
  --tw-contain-layout: ;
  --tw-contain-paint: ;
  --tw-contain-style: ;
}

::backdrop {
  --tw-border-spacing-x: 0;
  --tw-border-spacing-y: 0;
  --tw-translate-x: 0;
  --tw-translate-y: 0;
  --tw-rotate: 0;
  --tw-skew-x: 0;
  --tw-skew-y: 0;
  --tw-scale-x: 1;
  --tw-scale-y: 1;
  --tw-pan-x: ;
  --tw-pan-y: ;
  --tw-pinch-zoom: ;
  --tw-scroll-snap-strictness: proximity;
  --tw-gradient-from-position: ;
  --tw-gradient-via-position: ;
  --tw-gradient-to-position: ;
  --tw-ordinal: ;
  --tw-slashed-zero: ;
  --tw-numeric-figure: ;
  --tw-numeric-spacing: ;
  --tw-numeric-fraction: ;
  --tw-ring-inset: ;
  --tw-ring-offset-width: 0px;
  --tw-ring-offset-color: #fff;
  --tw-ring-color: #3b82f680;
  --tw-ring-offset-shadow: 0 0 #0000;
  --tw-ring-shadow: 0 0 #0000;
  --tw-shadow: 0 0 #0000;
  --tw-shadow-colored: 0 0 #0000;
  --tw-blur: ;
  --tw-brightness: ;
  --tw-contrast: ;
  --tw-grayscale: ;
  --tw-hue-rotate: ;
  --tw-invert: ;
  --tw-saturate: ;
  --tw-sepia: ;
  --tw-drop-shadow: ;
  --tw-backdrop-blur: ;
  --tw-backdrop-brightness: ;
  --tw-backdrop-contrast: ;
  --tw-backdrop-grayscale: ;
  --tw-backdrop-hue-rotate: ;
  --tw-backdrop-invert: ;
  --tw-backdrop-opacity: ;
  --tw-backdrop-saturate: ;
  --tw-backdrop-sepia: ;
  --tw-contain-size: ;
  --tw-contain-layout: ;
  --tw-contain-paint: ;
  --tw-contain-style: ;
}

*, :before, :after {
  box-sizing: border-box;
  border-width: 0;
  border-style: solid;
  border-color: #e5e7eb;
}

:before, :after {
  --tw-content: "";
}

html, :host {
  line-height: 1.5;
  -webkit-text-size-adjust: 100%;
  -moz-tab-size: 4;
  tab-size: 4;
  font-family: ui-sans-serif, system-ui, sans-serif, Apple Color Emoji, Segoe UI Emoji, Segoe UI Symbol, Noto Color Emoji;
  font-feature-settings: normal;
  font-variation-settings: normal;
  -webkit-tap-highlight-color: transparent;
}

body {
  margin: 0;
  line-height: inherit;
}

hr {
  height: 0;
  color: inherit;
  border-top-width: 1px;
}

abbr:where([title]) {
  text-decoration: underline dotted;
}

h1, h2, h3, h4, h5, h6 {
  font-size: inherit;
  font-weight: inherit;
}

a {
  color: inherit;
  text-decoration: inherit;
}

b, strong {
  font-weight: bolder;
}

code, kbd, samp, pre {
  font-family: ui-monospace, SFMono-Regular, Menlo, Monaco, Consolas, Liberation Mono, Courier New, monospace;
  font-feature-settings: normal;
  font-variation-settings: normal;
  font-size: 1em;
}

small {
  font-size: 80%;
}

sub, sup {
  font-size: 75%;
  line-height: 0;
  position: relative;
  vertical-align: baseline;
}

sub {
  bottom: -.25em;
}

sup {
  top: -.5em;
}

table {
  text-indent: 0;
  border-color: inherit;
  border-collapse: collapse;
}

button, input, optgroup, select, textarea {
  font-family: inherit;
  font-feature-settings: inherit;
  font-variation-settings: inherit;
  font-size: 100%;
  font-weight: inherit;
  line-height: inherit;
  letter-spacing: inherit;
  color: inherit;
  margin: 0;
  padding: 0;
}

button, select {
  text-transform: none;
}

button, input:where([type="button"]), input:where([type="reset"]), input:where([type="submit"]) {
  -webkit-appearance: button;
  background-color: #0000;
  background-image: none;
}

:-moz-focusring {
  outline: auto;
}

:-moz-ui-invalid {
  box-shadow: none;
}

progress {
  vertical-align: baseline;
}

::-webkit-inner-spin-button, ::-webkit-outer-spin-button {
  height: auto;
}

[type="search"] {
  -webkit-appearance: textfield;
  outline-offset: -2px;
}

::-webkit-search-decoration {
  -webkit-appearance: none;
}

::-webkit-file-upload-button {
  -webkit-appearance: button;
  font: inherit;
}

summary {
  display: list-item;
}

blockquote, dl, dd, h1, h2, h3, h4, h5, h6, hr, figure, p, pre {
  margin: 0;
}

fieldset {
  margin: 0;
  padding: 0;
}

legend {
  padding: 0;
}

ol, ul, menu {
  list-style: none;
  margin: 0;
  padding: 0;
}

dialog {
  padding: 0;
}

textarea {
  resize: vertical;
}

input::placeholder, textarea::placeholder {
  opacity: 1;
  color: #9ca3af;
}

button, [role="button"] {
  cursor: pointer;
}

:disabled {
  cursor: default;
}

img, svg, video, canvas, audio, iframe, embed, object {
  display: block;
  vertical-align: middle;
}

img, video {
  max-width: 100%;
  height: auto;
}

[hidden]:where(:not([hidden="until-found"])) {
  display: none;
}

.container {
  width: 100%;
}

@media (width >= 640px) {
  .container {
    max-width: 640px;
  }
}

@media (width >= 768px) {
  .container {
    max-width: 768px;
  }
}

@media (width >= 1024px) {
  .container {
    max-width: 1024px;
  }
}

@media (width >= 1280px) {
  .container {
    max-width: 1280px;
  }
}

@media (width >= 1536px) {
  .container {
    max-width: 1536px;
  }
}

.visible {
  visibility: visible;
}

.static {
  position: static;
}

.fixed {
  position: fixed;
}

.absolute {
  position: absolute;
}

.relative {
  position: relative;
}

.inset-0 {
  inset: 0;
}

.end-0 {
  inset-inline-end: 0;
}

.start-0 {
  inset-inline-start: 0;
}

.top-1 {
  top: .25rem;
}

.z-40 {
  z-index: 40;
}

.z-50 {
  z-index: 50;
}

.order-1 {
  order: 1;
}

.order-2 {
  order: 2;
}

.order-3 {
  order: 3;
}

.m-2 {
  margin: .5rem;
}

.m-auto {
  margin: auto;
}

.mx-1 {
  margin-left: .25rem;
  margin-right: .25rem;
}

.mx-2 {
  margin-left: .5rem;
  margin-right: .5rem;
}

.my-10 {
  margin-top: 2.5rem;
  margin-bottom: 2.5rem;
}

.my-2 {
  margin-top: .5rem;
  margin-bottom: .5rem;
}

.my-3 {
  margin-top: .75rem;
  margin-bottom: .75rem;
}

.my-4 {
  margin-top: 1rem;
  margin-bottom: 1rem;
}

.my-5 {
  margin-top: 1.25rem;
  margin-bottom: 1.25rem;
}

.mb-0 {
  margin-bottom: 0;
}

.mb-1 {
  margin-bottom: .25rem;
}

.mb-2 {
  margin-bottom: .5rem;
}

.mb-3 {
  margin-bottom: .75rem;
}

.mb-4 {
  margin-bottom: 1rem;
}

.mb-5 {
  margin-bottom: 1.25rem;
}

.mb-6 {
  margin-bottom: 1.5rem;
}

.me-0 {
  margin-inline-end: 0;
}

.me-2 {
  margin-inline-end: .5rem;
}

.me-3 {
  margin-inline-end: .75rem;
}

.ml-1 {
  margin-left: .25rem;
}

.ml-2 {
  margin-left: .5rem;
}

.ml-3 {
  margin-left: .75rem;
}

.ms-1 {
  margin-inline-start: .25rem;
}

.ms-2 {
  margin-inline-start: .5rem;
}

.ms-3 {
  margin-inline-start: .75rem;
}

.mt-0 {
  margin-top: 0;
}

.mt-1 {
  margin-top: .25rem;
}

.mt-10 {
  margin-top: 2.5rem;
}

.mt-2 {
  margin-top: .5rem;
}

.mt-20 {
  margin-top: 5rem;
}

.mt-3 {
  margin-top: .75rem;
}

.mt-4 {
  margin-top: 1rem;
}

.mt-5 {
  margin-top: 1.25rem;
}

.mt-8 {
  margin-top: 2rem;
}

.block {
  display: block;
}

.inline-block {
  display: inline-block;
}

.inline {
  display: inline;
}

.flex {
  display: flex;
}

.inline-flex {
  display: inline-flex;
}

.table {
  display: table;
}

.table-row {
  display: table-row;
}

.hidden {
  display: none;
}

.h-auto {
  height: auto;
}

.w-48 {
  width: 12rem;
}

.w-auto {
  width: auto;
}

.w-full {
  width: 100%;
}

.min-w-\[100px\] {
  min-width: 100px;
}

.min-w-\[120px\] {
  min-width: 120px;
}

.min-w-\[140px\] {
  min-width: 140px;
}

.min-w-\[160px\] {
  min-width: 160px;
}

.min-w-\[800px\] {
  min-width: 800px;
}

.min-w-\[80px\] {
  min-width: 80px;
}

.max-w-\[160px\] {
  max-width: 160px;
}

.max-w-\[200px\] {
  max-width: 200px;
}

.max-w-full {
  max-width: 100%;
}

.flex-1 {
  flex: 1;
}

.shrink-0 {
  flex-shrink: 0;
}

.grow {
  flex-grow: 1;
}

.origin-top {
  transform-origin: top;
}

.translate-y-0 {
  --tw-translate-y: 0px;
  transform: translate(var(--tw-translate-x), var(--tw-translate-y)) rotate(var(--tw-rotate)) skewX(var(--tw-skew-x)) skewY(var(--tw-skew-y)) scaleX(var(--tw-scale-x)) scaleY(var(--tw-scale-y));
}

.translate-y-4 {
  --tw-translate-y: 1rem;
  transform: translate(var(--tw-translate-x), var(--tw-translate-y)) rotate(var(--tw-rotate)) skewX(var(--tw-skew-x)) skewY(var(--tw-skew-y)) scaleX(var(--tw-scale-x)) scaleY(var(--tw-scale-y));
}

.scale-100 {
  --tw-scale-x: 1;
  --tw-scale-y: 1;
  transform: translate(var(--tw-translate-x), var(--tw-translate-y)) rotate(var(--tw-rotate)) skewX(var(--tw-skew-x)) skewY(var(--tw-skew-y)) scaleX(var(--tw-scale-x)) scaleY(var(--tw-scale-y));
}

.scale-95 {
  --tw-scale-x: .95;
  --tw-scale-y: .95;
  transform: translate(var(--tw-translate-x), var(--tw-translate-y)) rotate(var(--tw-rotate)) skewX(var(--tw-skew-x)) skewY(var(--tw-skew-y)) scaleX(var(--tw-scale-x)) scaleY(var(--tw-scale-y));
}

.transform {
  transform: translate(var(--tw-translate-x), var(--tw-translate-y)) rotate(var(--tw-rotate)) skewX(var(--tw-skew-x)) skewY(var(--tw-skew-y)) scaleX(var(--tw-scale-x)) scaleY(var(--tw-scale-y));
}

.cursor-pointer {
  cursor: pointer;
}

.resize-none {
  resize: none;
}

.resize {
  resize: both;
}

.flex-col {
  flex-direction: column;
}

.flex-wrap {
  flex-wrap: wrap;
}

.content-center {
  align-content: center;
}

.items-start {
  align-items: flex-start;
}

.items-center {
  align-items: center;
}

.justify-end {
  justify-content: flex-end;
}

.justify-between {
  justify-content: space-between;
}

.justify-stretch {
  justify-content: stretch;
}

.gap-1 {
  gap: .25rem;
}

.gap-2 {
  gap: .5rem;
}

.gap-3 {
  gap: .75rem;
}

.gap-4 {
  gap: 1rem;
}

.space-y-4 > :not([hidden]) ~ :not([hidden]) {
  --tw-space-y-reverse: 0;
  margin-top: calc(1rem * calc(1 - var(--tw-space-y-reverse)));
  margin-bottom: calc(1rem * var(--tw-space-y-reverse));
}

.overflow-hidden {
  overflow: hidden;
}

.overflow-x-auto {
  overflow-x: auto;
}

.overflow-y-auto {
  overflow-y: auto;
}

.whitespace-nowrap {
  white-space: nowrap;
}

.text-nowrap {
  text-wrap: nowrap;
}

.break-words {
  overflow-wrap: break-word;
}

.\!rounded-md {
  border-radius: .375rem !important;
}

.rounded {
  border-radius: .25rem;
}

.rounded-\[15px\] {
  border-radius: 15px;
}

.rounded-lg {
  border-radius: .5rem;
}

.rounded-md {
  border-radius: .375rem;
}

.border {
  border-width: 1px;
}

.border-b {
  border-bottom-width: 1px;
}

.border-l-4 {
  border-left-width: 4px;
}

.border-gray-300 {
  --tw-border-opacity: 1;
  border-color: rgb(209 213 219 / var(--tw-border-opacity, 1));
}

.border-gray-600 {
  --tw-border-opacity: 1;
  border-color: rgb(75 85 99 / var(--tw-border-opacity, 1));
}

.border-indigo-400 {
  --tw-border-opacity: 1;
  border-color: rgb(129 140 248 / var(--tw-border-opacity, 1));
}

.border-transparent {
  border-color: #0000;
}

.\!bg-\[\#B4B4B4\] {
  --tw-bg-opacity: 1 !important;
  background-color: rgb(180 180 180 / var(--tw-bg-opacity, 1)) !important;
}

.bg-\[\#00b7ff\] {
  --tw-bg-opacity: 1;
  background-color: rgb(0 183 255 / var(--tw-bg-opacity, 1));
}

.bg-gray-200 {
  --tw-bg-opacity: 1;
  background-color: rgb(229 231 235 / var(--tw-bg-opacity, 1));
}

.bg-gray-500\/75 {
  background-color: #6b7280bf;
}

.bg-gray-800 {
  --tw-bg-opacity: 1;
  background-color: rgb(31 41 55 / var(--tw-bg-opacity, 1));
}

.bg-indigo-50 {
  --tw-bg-opacity: 1;
  background-color: rgb(238 242 255 / var(--tw-bg-opacity, 1));
}

.bg-red-600 {
  --tw-bg-opacity: 1;
  background-color: rgb(220 38 38 / var(--tw-bg-opacity, 1));
}

.bg-white {
  --tw-bg-opacity: 1;
  background-color: rgb(255 255 255 / var(--tw-bg-opacity, 1));
}

.bg-white\/20 {
  background-color: #fff3;
}

.p-0 {
  padding: 0;
}

.p-1 {
  padding: .25rem;
}

.p-2 {
  padding: .5rem;
}

.p-3 {
  padding: .75rem;
}

.p-4 {
  padding: 1rem;
}

.p-8 {
  padding: 2rem;
}

.px-0 {
  padding-left: 0;
  padding-right: 0;
}

.px-1 {
  padding-left: .25rem;
  padding-right: .25rem;
}

.px-2 {
  padding-left: .5rem;
  padding-right: .5rem;
}

.px-3 {
  padding-left: .75rem;
  padding-right: .75rem;
}

.px-4 {
  padding-left: 1rem;
  padding-right: 1rem;
}

.py-1 {
  padding-top: .25rem;
  padding-bottom: .25rem;
}

.py-10 {
  padding-top: 2.5rem;
  padding-bottom: 2.5rem;
}

.py-2 {
  padding-top: .5rem;
  padding-bottom: .5rem;
}

.py-3 {
  padding-top: .75rem;
  padding-bottom: .75rem;
}

.py-4 {
  padding-top: 1rem;
  padding-bottom: 1rem;
}

.py-40 {
  padding-top: 10rem;
  padding-bottom: 10rem;
}

.py-5 {
  padding-top: 1.25rem;
  padding-bottom: 1.25rem;
}

.py-6 {
  padding-top: 1.5rem;
  padding-bottom: 1.5rem;
}

.pb-1 {
  padding-bottom: .25rem;
}

.pb-10 {
  padding-bottom: 2.5rem;
}

.pb-2 {
  padding-bottom: .5rem;
}

.pb-3 {
  padding-bottom: .75rem;
}

.pb-4 {
  padding-bottom: 1rem;
}

.pb-5 {
  padding-bottom: 1.25rem;
}

.pb-6 {
  padding-bottom: 1.5rem;
}

.pe-0 {
  padding-inline-end: 0;
}

.pe-1 {
  padding-inline-end: .25rem;
}

.pe-3 {
  padding-inline-end: .75rem;
}

.pe-4 {
  padding-inline-end: 1rem;
}

.ps-1 {
  padding-inline-start: .25rem;
}

.ps-2 {
  padding-inline-start: .5rem;
}

.ps-3 {
  padding-inline-start: .75rem;
}

.ps-4 {
  padding-inline-start: 1rem;
}

.pt-0 {
  padding-top: 0;
}

.pt-2 {
  padding-top: .5rem;
}

.pt-3 {
  padding-top: .75rem;
}

.pt-4 {
  padding-top: 1rem;
}

.pt-5 {
  padding-top: 1.25rem;
}

.text-left {
  text-align: left;
}

.text-center {
  text-align: center;
}

.text-start {
  text-align: start;
}

.text-end {
  text-align: end;
}

.align-top {
  vertical-align: top;
}

.text-2xl {
  font-size: 1.5rem;
  line-height: 2rem;
}

.text-\[18px\] {
  font-size: 18px;
}

.text-\[24px\] {
  font-size: 24px;
}

.text-base {
  font-size: 1rem;
  line-height: 1.5rem;
}

.text-sm {
  font-size: .875rem;
  line-height: 1.25rem;
}

.text-xl {
  font-size: 1.25rem;
  line-height: 1.75rem;
}

.text-xs {
  font-size: .75rem;
  line-height: 1rem;
}

.font-bold {
  font-weight: 700;
}

.font-extrabold {
  font-weight: 800;
}

.font-medium {
  font-weight: 500;
}

.font-semibold {
  font-weight: 600;
}

.uppercase {
  text-transform: uppercase;
}

.lowercase {
  text-transform: lowercase;
}

.leading-5 {
  line-height: 1.25rem;
}

.leading-tight {
  line-height: 1.25;
}

.tracking-wide {
  letter-spacing: .025em;
}

.tracking-widest {
  letter-spacing: .1em;
}

.text-\[\#00b7ff\] {
  --tw-text-opacity: 1;
  color: rgb(0 183 255 / var(--tw-text-opacity, 1));
}

.text-\[\#32CD33\] {
  --tw-text-opacity: 1;
  color: rgb(50 205 51 / var(--tw-text-opacity, 1));
}

.text-black {
  --tw-text-opacity: 1;
  color: rgb(0 0 0 / var(--tw-text-opacity, 1));
}

.text-blue-500 {
  --tw-text-opacity: 1;
  color: rgb(59 130 246 / var(--tw-text-opacity, 1));
}

.text-gray-300 {
  --tw-text-opacity: 1;
  color: rgb(209 213 219 / var(--tw-text-opacity, 1));
}

.text-gray-600 {
  --tw-text-opacity: 1;
  color: rgb(75 85 99 / var(--tw-text-opacity, 1));
}

.text-gray-700 {
  --tw-text-opacity: 1;
  color: rgb(55 65 81 / var(--tw-text-opacity, 1));
}

.text-indigo-600 {
  --tw-text-opacity: 1;
  color: rgb(79 70 229 / var(--tw-text-opacity, 1));
}

.text-indigo-700 {
  --tw-text-opacity: 1;
  color: rgb(67 56 202 / var(--tw-text-opacity, 1));
}

.text-white {
  --tw-text-opacity: 1;
  color: rgb(255 255 255 / var(--tw-text-opacity, 1));
}

.underline {
  text-decoration-line: underline;
}

.opacity-0 {
  opacity: 0;
}

.opacity-100 {
  opacity: 1;
}

.opacity-25 {
  opacity: .25;
}

.shadow-lg {
  --tw-shadow: 0 10px 15px -3px #0000001a, 0 4px 6px -4px #0000001a;
  --tw-shadow-colored: 0 10px 15px -3px var(--tw-shadow-color), 0 4px 6px -4px var(--tw-shadow-color);
  box-shadow: var(--tw-ring-offset-shadow, 0 0 #0000), var(--tw-ring-shadow, 0 0 #0000), var(--tw-shadow);
}

.shadow-sm {
  --tw-shadow: 0 1px 2px 0 #0000000d;
  --tw-shadow-colored: 0 1px 2px 0 var(--tw-shadow-color);
  box-shadow: var(--tw-ring-offset-shadow, 0 0 #0000), var(--tw-ring-shadow, 0 0 #0000), var(--tw-shadow);
}

.shadow-xl {
  --tw-shadow: 0 20px 25px -5px #0000001a, 0 8px 10px -6px #0000001a;
  --tw-shadow-colored: 0 20px 25px -5px var(--tw-shadow-color), 0 8px 10px -6px var(--tw-shadow-color);
  box-shadow: var(--tw-ring-offset-shadow, 0 0 #0000), var(--tw-ring-shadow, 0 0 #0000), var(--tw-shadow);
}

.outline-none {
  outline: 2px solid #0000;
  outline-offset: 2px;
}

.ring-1 {
  --tw-ring-offset-shadow: var(--tw-ring-inset) 0 0 0 var(--tw-ring-offset-width) var(--tw-ring-offset-color);
  --tw-ring-shadow: var(--tw-ring-inset) 0 0 0 calc(1px + var(--tw-ring-offset-width)) var(--tw-ring-color);
  box-shadow: var(--tw-ring-offset-shadow), var(--tw-ring-shadow), var(--tw-shadow, 0 0 #0000);
}

.ring-black {
  --tw-ring-opacity: 1;
  --tw-ring-color: rgb(0 0 0 / var(--tw-ring-opacity, 1));
}

.ring-opacity-5 {
  --tw-ring-opacity: .05;
}

.invert {
  --tw-invert: invert(100%);
  filter: var(--tw-blur) var(--tw-brightness) var(--tw-contrast) var(--tw-grayscale) var(--tw-hue-rotate) var(--tw-invert) var(--tw-saturate) var(--tw-sepia) var(--tw-drop-shadow);
}

.filter {
  filter: var(--tw-blur) var(--tw-brightness) var(--tw-contrast) var(--tw-grayscale) var(--tw-hue-rotate) var(--tw-invert) var(--tw-saturate) var(--tw-sepia) var(--tw-drop-shadow);
}

.transition {
  transition-property: color, background-color, border-color, text-decoration-color, fill, stroke, opacity, box-shadow, transform, filter, backdrop-filter;
  transition-timing-function: cubic-bezier(.4, 0, .2, 1);
  transition-duration: .15s;
}

.transition-all {
  transition-property: all;
  transition-timing-function: cubic-bezier(.4, 0, .2, 1);
  transition-duration: .15s;
}

.transition-opacity {
  transition-property: opacity;
  transition-timing-function: cubic-bezier(.4, 0, .2, 1);
  transition-duration: .15s;
}

.duration-150 {
  transition-duration: .15s;
}

.duration-200 {
  transition-duration: .2s;
}

.duration-300 {
  transition-duration: .3s;
}

.duration-75 {
  transition-duration: 75ms;
}

.ease-in {
  transition-timing-function: cubic-bezier(.4, 0, 1, 1);
}

.ease-in-out {
  transition-timing-function: cubic-bezier(.4, 0, .2, 1);
}

.ease-out {
  transition-timing-function: cubic-bezier(0, 0, .2, 1);
}

:root {
  --foreground: #fff;
  --background: #011132;
  --font-gilroy: "Gilroy", sans-serif;
}

@media (prefers-color-scheme: light) {
  :root {
    --background: #011132;
    --foreground: #ededed;
  }
}

body {
  color: var(--foreground);
  background: var(--background);
  font-family: var(--font-gilroy);
}

.osano-cm-widget {
  display: none;
}

.osano-cm-dialog {
  border: 1px solid #00719d;
}

.arrow-right {
  border-radius: 10rem;
  z-index: 2;
  width: 50px !important;
  height: 50px !important;
  background-color: #00adef !important;
}

.slick-prev, .slick-next {
  position: relative !important;
  left: 0 !important;
  right: 0 !important;
}

.popup {
  position: absolute;
  max-width: 90vw;
  max-height: 90vh;
  overflow: auto;
  background: #fff;
  border-radius: 8px;
  box-shadow: 0 4px 10px #0003;
  z-index: 1000;
}

.nextjs-toast-errors-parent {
  display: none;
}

.text-sec {
  color: #fff;
}

.font-14 {
  font-size: 14px;
}

.font-18 {
  font-size: 18px;
}

.popup-container {
  position: fixed;
  inset: 0;
  display: flex;
  justify-content: center;
  align-items: center;
  padding: 10px;
}

.min-h-500 {
  height: 600px !important;
  min-height: 600px !important;
  min-width: 350px !important;
}

.caret {
  animation: 1s step-end infinite blink;
}

.scroll-lock {
  position: fixed;
  width: 100%;
  overflow: hidden;
}

@keyframes blink {
  50% {
    opacity: 0;
  }
}

.new-link {
  transition: all .3s ease-in-out;
  color: #00adef !important;
}

.text-md-nowrap {
  white-space: nowrap;
}

@media (width <= 700px) {
  .text-md-nowrap {
    white-space: unset;
  }
}

.scroll-table {
  max-height: 270px;
  overflow-y: scroll;
}

.bg-trans > * {
  background-color: #0000;
}

.jodit-container {
  background: #fff !important;
}

.jodit-container .jodit-wysiwyg {
  min-height: 300px;
  padding: 10px;
  font-size: 16px;
  background: #8b7c7c !important;
  color: #000 !important;
}

.jodit-container .jodit-toolbar {
  border-bottom: 1px solid #ddd;
  background: #f8f9fa !important;
}

.cart_button {
  width: 70%;
  border-radius: 10px !important;
}

.cart_select {
  padding: 0 20px;
  border-radius: 10px;
  width: 25%;
  background-color: #fff;
  color: #000;
  border: 1px solid #0000;
}

.bb-blue {
  border-bottom: 3px solid #00adef4d;
}

.ws-normal {
  white-space: normal !important;
}

.txt-blue {
  color: #00adef;
}

.search-highlight {
  color: #04498c;
  font-weight: 600 !important;
}

.scroll-hidden {
  overflow-y: auto;
  max-height: 100vh;
}

.scroll-hidden::-webkit-scrollbar {
  display: none;
}

a, a:hover {
  text-decoration: none;
  transition: all .3s ease-in-out;
  color: #00adef;
}

h1, .h1 {
  font-size: 3rem;
  font-weight: 800;
}

@media (width <= 1199px) {
  h1, .h1 {
    font-size: 2.5rem;
  }
}

@media (width <= 767px) {
  h1, .h1 {
    font-size: 1.5rem;
  }
}

@media (width <= 390px) {
  h1, .h1 {
    font-size: 1.3rem;
  }
}

h2, .h2 {
  font-size: 5rem;
  font-weight: 800;
}

@media (width <= 1269px) {
  h2, .h2 {
    font-size: 3rem;
  }
}

@media (width <= 767px) {
  h2, .h2 {
    font-size: 1.363rem;
  }
}

h3, .h3 {
  font-size: 2.8rem;
  font-weight: 800;
}

@media (width <= 1199px) {
  h3, .h3 {
    font-size: 1.688rem;
  }
}

@media (width <= 767px) {
  h3, .h3 {
    font-size: 1.25rem;
  }
}

h4, .h4 {
  font-size: 1.65rem;
  line-height: 35px;
  font-weight: 600;
}

@media (width <= 767px) {
  h4, .h4 {
    font-size: 1.15rem;
    line-height: 25px;
  }
}

h5, .h5 {
  font-size: 1.25rem;
  line-height: 30px;
  font-weight: 600;
}

@media (width <= 767px) {
  h5, .h5 {
    font-size: 1rem;
    line-height: 25px;
  }
}

h6, .h6 {
  font-size: 1.125rem;
  font-weight: 600;
}

@media (width <= 767px) {
  h6, .h6 {
    font-size: 1rem;
  }
}

p {
  font-size: 1rem;
  font-weight: 400;
}

@media (width <= 767px) {
  p {
    font-size: .875rem;
  }
}

.content-center {
  display: flex;
  justify-content: center;
  align-items: center;
}

.custom_checkbox {
  margin-bottom: 1.25rem;
  display: flex;
  align-items: center;
  gap: 10px;
}

.custom_checkbox_input {
  height: 20px !important;
  width: 20px !important;
  margin-top: 0 !important;
  background-color: #0000 !important;
  border: 1px solid #00adef !important;
}

.custom_checkbox_input:focus {
  box-shadow: none !important;
}

.custom_checkbox_input:checked {
  background-color: #00adef !important;
  border: 1px solid #00adef !important;
}

.custom_checkbox_label {
  color: #fff;
  font-size: 15px;
  font-weight: 600;
  cursor: pointer;
}

.switch {
  position: relative;
  display: inline-block;
  width: 50px;
  height: 28px;
}

.switch input {
  opacity: 0;
  width: 0;
  height: 0;
}

.switch-label {
  color: #fff;
  font-size: 18px;
  font-weight: 600;
  cursor: pointer;
}

@media (width <= 550px) {
  .switch-label {
    font-size: 16px;
  }
}

@media (width <= 350px) {
  .switch-label {
    font-size: 14px;
  }
}

.slider {
  position: absolute;
  cursor: pointer;
  top: 0;
  left: 0;
  right: 0;
  bottom: 0;
  background-color: #fff;
  transition: all .4s;
  border-radius: 34px;
}

.slider:before {
  position: absolute;
  content: "";
  height: 22px;
  width: 22px;
  left: 3px;
  bottom: 3px;
  background-color: #9c9a9f;
  transition: all .4s;
  border-radius: 50%;
}

input:checked + .slider {
  background-color: #0099d1;
}

input:checked + .slider:before {
  transform: translateX(22px);
  background-color: #fff;
}

.hover\:border-gray-300:hover {
  --tw-border-opacity: 1;
  border-color: rgb(209 213 219 / var(--tw-border-opacity, 1));
}

.hover\:bg-gray-100:hover {
  --tw-bg-opacity: 1;
  background-color: rgb(243 244 246 / var(--tw-bg-opacity, 1));
}

.hover\:bg-gray-50:hover {
  --tw-bg-opacity: 1;
  background-color: rgb(249 250 251 / var(--tw-bg-opacity, 1));
}

.hover\:bg-gray-700:hover {
  --tw-bg-opacity: 1;
  background-color: rgb(55 65 81 / var(--tw-bg-opacity, 1));
}

.hover\:bg-red-500:hover {
  --tw-bg-opacity: 1;
  background-color: rgb(239 68 68 / var(--tw-bg-opacity, 1));
}

.hover\:text-gray-800:hover {
  --tw-text-opacity: 1;
  color: rgb(31 41 55 / var(--tw-text-opacity, 1));
}

.focus\:border-gray-300:focus {
  --tw-border-opacity: 1;
  border-color: rgb(209 213 219 / var(--tw-border-opacity, 1));
}

.focus\:border-indigo-700:focus {
  --tw-border-opacity: 1;
  border-color: rgb(67 56 202 / var(--tw-border-opacity, 1));
}

.focus\:bg-gray-100:focus {
  --tw-bg-opacity: 1;
  background-color: rgb(243 244 246 / var(--tw-bg-opacity, 1));
}

.focus\:bg-gray-50:focus {
  --tw-bg-opacity: 1;
  background-color: rgb(249 250 251 / var(--tw-bg-opacity, 1));
}

.focus\:bg-gray-700:focus {
  --tw-bg-opacity: 1;
  background-color: rgb(55 65 81 / var(--tw-bg-opacity, 1));
}

.focus\:bg-indigo-100:focus {
  --tw-bg-opacity: 1;
  background-color: rgb(224 231 255 / var(--tw-bg-opacity, 1));
}

.focus\:text-gray-800:focus {
  --tw-text-opacity: 1;
  color: rgb(31 41 55 / var(--tw-text-opacity, 1));
}

.focus\:text-indigo-800:focus {
  --tw-text-opacity: 1;
  color: rgb(55 48 163 / var(--tw-text-opacity, 1));
}

.focus\:outline-none:focus {
  outline: 2px solid #0000;
  outline-offset: 2px;
}

.focus\:ring-2:focus {
  --tw-ring-offset-shadow: var(--tw-ring-inset) 0 0 0 var(--tw-ring-offset-width) var(--tw-ring-offset-color);
  --tw-ring-shadow: var(--tw-ring-inset) 0 0 0 calc(2px + var(--tw-ring-offset-width)) var(--tw-ring-color);
  box-shadow: var(--tw-ring-offset-shadow), var(--tw-ring-shadow), var(--tw-shadow, 0 0 #0000);
}

.focus\:ring-indigo-500:focus {
  --tw-ring-opacity: 1;
  --tw-ring-color: rgb(99 102 241 / var(--tw-ring-opacity, 1));
}

.focus\:ring-red-500:focus {
  --tw-ring-opacity: 1;
  --tw-ring-color: rgb(239 68 68 / var(--tw-ring-opacity, 1));
}

.focus\:ring-offset-2:focus {
  --tw-ring-offset-width: 2px;
}

.active\:bg-gray-900:active {
  --tw-bg-opacity: 1;
  background-color: rgb(17 24 39 / var(--tw-bg-opacity, 1));
}

.active\:bg-red-700:active {
  --tw-bg-opacity: 1;
  background-color: rgb(185 28 28 / var(--tw-bg-opacity, 1));
}

.disabled\:opacity-25:disabled {
  opacity: .25;
}

@media (width >= 640px) {
  .sm\:mx-auto {
    margin-left: auto;
    margin-right: auto;
  }

  .sm\:mt-0 {
    margin-top: 0;
  }

  .sm\:w-\[200px\] {
    width: 200px;
  }

  .sm\:w-auto {
    width: auto;
  }

  .sm\:w-full {
    width: 100%;
  }

  .sm\:max-w-2xl {
    max-width: 42rem;
  }

  .sm\:max-w-\[180px\] {
    max-width: 180px;
  }

  .sm\:max-w-lg {
    max-width: 32rem;
  }

  .sm\:max-w-md {
    max-width: 28rem;
  }

  .sm\:max-w-sm {
    max-width: 24rem;
  }

  .sm\:max-w-xl {
    max-width: 36rem;
  }

  .sm\:translate-y-0 {
    --tw-translate-y: 0px;
    transform: translate(var(--tw-translate-x), var(--tw-translate-y)) rotate(var(--tw-rotate)) skewX(var(--tw-skew-x)) skewY(var(--tw-skew-y)) scaleX(var(--tw-scale-x)) scaleY(var(--tw-scale-y));
  }

  .sm\:scale-100 {
    --tw-scale-x: 1;
    --tw-scale-y: 1;
    transform: translate(var(--tw-translate-x), var(--tw-translate-y)) rotate(var(--tw-rotate)) skewX(var(--tw-skew-x)) skewY(var(--tw-skew-y)) scaleX(var(--tw-scale-x)) scaleY(var(--tw-scale-y));
  }

  .sm\:scale-95 {
    --tw-scale-x: .95;
    --tw-scale-y: .95;
    transform: translate(var(--tw-translate-x), var(--tw-translate-y)) rotate(var(--tw-rotate)) skewX(var(--tw-skew-x)) skewY(var(--tw-skew-y)) scaleX(var(--tw-scale-x)) scaleY(var(--tw-scale-y));
  }

  .sm\:flex-row {
    flex-direction: row;
  }

  .sm\:gap-4 {
    gap: 1rem;
  }

  .sm\:space-y-6 > :not([hidden]) ~ :not([hidden]) {
    --tw-space-y-reverse: 0;
    margin-top: calc(1.5rem * calc(1 - var(--tw-space-y-reverse)));
    margin-bottom: calc(1.5rem * var(--tw-space-y-reverse));
  }

  .sm\:px-0 {
    padding-left: 0;
    padding-right: 0;
  }

  .sm\:px-2 {
    padding-left: .5rem;
    padding-right: .5rem;
  }

  .sm\:px-6 {
    padding-left: 1.5rem;
    padding-right: 1.5rem;
  }

  .sm\:py-10 {
    padding-top: 2.5rem;
    padding-bottom: 2.5rem;
  }

  .sm\:py-3 {
    padding-top: .75rem;
    padding-bottom: .75rem;
  }

  .sm\:text-left {
    text-align: left;
  }

  .sm\:text-\[16px\] {
    font-size: 16px;
  }

  .sm\:text-\[20px\] {
    font-size: 20px;
  }

  .sm\:text-\[32px\] {
    font-size: 32px;
  }

  .sm\:text-base {
    font-size: 1rem;
    line-height: 1.5rem;
  }

  .sm\:text-sm {
    font-size: .875rem;
    line-height: 1.25rem;
  }
}

@media (width >= 768px) {
  .md\:table-row {
    display: table-row;
  }

  .md\:hidden {
    display: none;
  }

  .md\:text-lg {
    font-size: 1.125rem;
    line-height: 1.75rem;
  }
}

@media (width >= 1024px) {
  .lg\:max-w-\[200px\] {
    max-width: 200px;
  }

  .lg\:px-4 {
    padding-left: 1rem;
    padding-right: 1rem;
  }

  .lg\:text-base {
    font-size: 1rem;
    line-height: 1.5rem;
  }
}

.ltr\:origin-top-left:where([dir="ltr"], [dir="ltr"] *) {
  transform-origin: 0 0;
}

.ltr\:origin-top-right:where([dir="ltr"], [dir="ltr"] *) {
  transform-origin: 100% 0;
}

.rtl\:origin-top-left:where([dir="rtl"], [dir="rtl"] *) {
  transform-origin: 0 0;
}

.rtl\:origin-top-right:where([dir="rtl"], [dir="rtl"] *) {
  transform-origin: 100% 0;
}

/*# sourceMappingURL=src_app_globals_scss_b52d8e88.css.map*/